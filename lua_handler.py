"""
كلاس للتعامل مع ملفات Lua
"""
import os
import shutil
from pathlib import Path
import json

class LuaHandler:
    def __init__(self, game_path):
        self.game_path = game_path
        self.lua_files = []
        self.backup_folder = os.path.join(game_path, "lua_backups")
        
    def find_lua_files(self):
        """البحث عن جميع ملفات Lua في مجلد العبة"""
        self.lua_files = []
        if not os.path.exists(self.game_path):
            return []
            
        for root, dirs, files in os.walk(self.game_path):
            for file in files:
                if file.endswith('.lua'):
                    full_path = os.path.join(root, file)
                    relative_path = os.path.relpath(full_path, self.game_path)
                    self.lua_files.append({
                        'name': file,
                        'full_path': full_path,
                        'relative_path': relative_path,
                        'size': os.path.getsize(full_path)
                    })
        
        return self.lua_files
    
    def create_backup(self, lua_file_path):
        """إنشاء نسخة احتياطية من ملف Lua"""
        if not os.path.exists(self.backup_folder):
            os.makedirs(self.backup_folder)
            
        file_name = os.path.basename(lua_file_path)
        backup_path = os.path.join(self.backup_folder, f"{file_name}.backup")
        
        try:
            shutil.copy2(lua_file_path, backup_path)
            return True
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def inject_lua_code(self, target_file, injection_code, position="end"):
        """حقن كود Lua في ملف موجود"""
        if not os.path.exists(target_file):
            return False
            
        # إنشاء نسخة احتياطية أولاً
        if not self.create_backup(target_file):
            return False
            
        try:
            with open(target_file, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            if position == "start":
                new_content = injection_code + "\n" + original_content
            elif position == "end":
                new_content = original_content + "\n" + injection_code
            else:  # position is a line number
                lines = original_content.split('\n')
                lines.insert(position, injection_code)
                new_content = '\n'.join(lines)
            
            with open(target_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
                
            return True
        except Exception as e:
            print(f"خطأ في حقن الكود: {e}")
            return False
    
    def replace_lua_file(self, target_file, new_file_path):
        """استبدال ملف Lua بملف جديد"""
        if not os.path.exists(new_file_path):
            return False
            
        # إنشاء نسخة احتياطية أولاً
        if not self.create_backup(target_file):
            return False
            
        try:
            shutil.copy2(new_file_path, target_file)
            return True
        except Exception as e:
            print(f"خطأ في استبدال الملف: {e}")
            return False
    
    def restore_backup(self, lua_file_path):
        """استعادة النسخة الاحتياطية"""
        file_name = os.path.basename(lua_file_path)
        backup_path = os.path.join(self.backup_folder, f"{file_name}.backup")
        
        if not os.path.exists(backup_path):
            return False
            
        try:
            shutil.copy2(backup_path, lua_file_path)
            return True
        except Exception as e:
            print(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return False
    
    def get_lua_content(self, lua_file_path):
        """قراءة محتوى ملف Lua"""
        try:
            with open(lua_file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"خطأ في قراءة الملف: {e}")
            return None
