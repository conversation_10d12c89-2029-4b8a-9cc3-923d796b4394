"""
فاحص الأجهزة المتصلة بالشبكة
"""
import socket
import subprocess
import threading
import time
import re
from netaddr import IPNetwork
import netifaces

class NetworkScanner:
    def __init__(self):
        self.devices = {}
        self.scanning = False
        self.local_ip = self.get_local_ip()
        self.network_range = self.get_network_range()
        
    def get_local_ip(self):
        """الحصول على IP المحلي"""
        try:
            # الاتصال بخادم خارجي لمعرفة IP المحلي
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "*************"  # قيمة افتراضية
    
    def get_network_range(self):
        """الحصول على نطاق الشبكة"""
        try:
            # الحصول على معلومات الشبكة
            gateways = netifaces.gateways()
            default_gateway = gateways['default'][netifaces.AF_INET][0]
            
            # تحديد نطاق الشبكة بناءً على Gateway
            ip_parts = default_gateway.split('.')
            network = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.0/24"
            return network
        except:
            return "***********/24"  # قيمة افتراضية
    
    def ping_host(self, ip):
        """فحص إذا كان الجهاز متصل"""
        try:
            # استخدام ping للتحقق من الاتصال
            result = subprocess.run(
                ['ping', '-n', '1', '-w', '1000', ip],
                capture_output=True,
                text=True,
                timeout=2
            )
            return result.returncode == 0
        except:
            return False
    
    def get_mac_address(self, ip):
        """الحصول على MAC Address للجهاز"""
        try:
            # استخدام ARP للحصول على MAC
            result = subprocess.run(
                ['arp', '-a', ip],
                capture_output=True,
                text=True,
                timeout=2
            )
            
            if result.returncode == 0:
                # البحث عن MAC في النتيجة
                mac_pattern = r'([0-9a-fA-F]{2}[:-]){5}([0-9a-fA-F]{2})'
                match = re.search(mac_pattern, result.stdout)
                if match:
                    return match.group(0)
            return "غير معروف"
        except:
            return "غير معروف"
    
    def get_device_name(self, ip):
        """محاولة الحصول على اسم الجهاز"""
        try:
            # محاولة الحصول على hostname
            hostname = socket.gethostbyaddr(ip)[0]
            return hostname
        except:
            try:
                # محاولة استخدام nbtstat (Windows)
                result = subprocess.run(
                    ['nbtstat', '-A', ip],
                    capture_output=True,
                    text=True,
                    timeout=3
                )
                
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if '<00>' in line and 'UNIQUE' in line:
                            name = line.split()[0].strip()
                            if name and name != ip:
                                return name
            except:
                pass
            
            return f"جهاز-{ip.split('.')[-1]}"
    
    def scan_single_ip(self, ip):
        """فحص IP واحد"""
        if self.ping_host(ip):
            mac = self.get_mac_address(ip)
            name = self.get_device_name(ip)
            
            device_info = {
                'ip': ip,
                'mac': mac,
                'name': name,
                'status': 'متصل',
                'last_seen': time.time(),
                'is_local': ip == self.local_ip
            }
            
            self.devices[ip] = device_info
            return device_info
        return None
    
    def scan_network(self, callback=None):
        """فحص جميع الأجهزة في الشبكة"""
        self.scanning = True
        self.devices.clear()
        
        print(f"بدء فحص الشبكة: {self.network_range}")
        
        # إنشاء قائمة بجميع IPs في النطاق
        network = IPNetwork(self.network_range)
        ips = [str(ip) for ip in network.iter_hosts()]
        
        # إضافة Gateway و Local IP
        gateway = netifaces.gateways()['default'][netifaces.AF_INET][0]
        if gateway not in ips:
            ips.append(gateway)
        if self.local_ip not in ips:
            ips.append(self.local_ip)
        
        def scan_batch(ip_batch):
            for ip in ip_batch:
                if not self.scanning:
                    break
                    
                device = self.scan_single_ip(ip)
                if device and callback:
                    callback(device)
        
        # تقسيم IPs إلى مجموعات للمعالجة المتوازية
        batch_size = 20
        threads = []
        
        for i in range(0, len(ips), batch_size):
            batch = ips[i:i + batch_size]
            thread = threading.Thread(target=scan_batch, args=(batch,))
            threads.append(thread)
            thread.start()
        
        # انتظار انتهاء جميع الخيوط
        for thread in threads:
            thread.join()
        
        self.scanning = False
        print(f"انتهى الفحص. تم العثور على {len(self.devices)} جهاز")
        return self.devices
    
    def stop_scan(self):
        """إيقاف الفحص"""
        self.scanning = False
    
    def get_device_vendor(self, mac):
        """محاولة تحديد نوع الجهاز من MAC"""
        if mac == "غير معروف":
            return "غير معروف"
            
        # قاعدة بيانات بسيطة لبعض الشركات المعروفة
        vendors = {
            '00:50:56': 'VMware',
            '08:00:27': 'VirtualBox',
            '00:0C:29': 'VMware',
            '00:1B:21': 'Intel',
            '00:23:24': 'Apple',
            '28:CF:E9': 'Apple',
            '3C:07:54': 'Apple',
            'B8:27:EB': 'Raspberry Pi',
            'DC:A6:32': 'Raspberry Pi',
            '00:E0:4C': 'Realtek',
            '00:50:B6': 'Matrix',
        }
        
        mac_prefix = mac[:8].upper()
        return vendors.get(mac_prefix, "غير معروف")
    
    def update_device_info(self):
        """تحديث معلومات الأجهزة المكتشفة"""
        for ip, device in self.devices.items():
            # إضافة معلومات إضافية
            device['vendor'] = self.get_device_vendor(device['mac'])
            device['connection_time'] = time.time() - device['last_seen']
    
    def get_network_info(self):
        """الحصول على معلومات الشبكة العامة"""
        try:
            gateway = netifaces.gateways()['default'][netifaces.AF_INET][0]
            interface = netifaces.gateways()['default'][netifaces.AF_INET][1]
            
            return {
                'local_ip': self.local_ip,
                'gateway': gateway,
                'network_range': self.network_range,
                'interface': interface,
                'total_devices': len(self.devices)
            }
        except:
            return {
                'local_ip': self.local_ip,
                'gateway': 'غير معروف',
                'network_range': self.network_range,
                'interface': 'غير معروف',
                'total_devices': len(self.devices)
            }
