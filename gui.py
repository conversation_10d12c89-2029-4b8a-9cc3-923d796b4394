"""
واجهة المستخدم الرسومية لـ Steam Lua Injector
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
from steam_injector import SteamInjector

class SteamInjectorGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Steam Lua Injector")
        self.root.geometry("800x600")
        self.root.direction = "rtl"  # للدعم العربي
        
        self.injector = SteamInjector()
        self.current_lua_files = []
        self.search_results = []
        self.installed_games = []

        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # إنشاء تبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # تبويب العبة الواحدة
        self.single_game_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.single_game_frame, text="عبة واحدة")

        # تبويب البحث
        self.search_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.search_frame, text="البحث في الألعاب")

        # تبويب الألعاب المثبتة
        self.installed_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.installed_frame, text="الألعاب المثبتة")

        # تبويب الحقن الجماعي
        self.batch_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.batch_frame, text="الحقن الجماعي")

        # إعداد كل تبويب
        self.setup_single_game_tab()
        self.setup_search_tab()
        self.setup_installed_tab()
        self.setup_batch_tab()

        # شريط الحالة
        self.status_var = tk.StringVar(value="جاهز")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(10, 0))

        # إعداد التوسيع
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(0, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

    def setup_single_game_tab(self):
        """إعداد تبويب العبة الواحدة"""
        # إعداد العبة
        game_frame = ttk.LabelFrame(self.single_game_frame, text="إعدادات العبة", padding="5")
        game_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(game_frame, text="App ID:").grid(row=0, column=0, sticky=tk.W)
        self.app_id_var = tk.StringVar()
        self.app_id_entry = ttk.Entry(game_frame, textvariable=self.app_id_var, width=20)
        self.app_id_entry.grid(row=0, column=1, padx=(5, 10))

        self.load_game_btn = ttk.Button(game_frame, text="تحميل العبة", command=self.load_game)
        self.load_game_btn.grid(row=0, column=2)

        self.game_info_label = ttk.Label(game_frame, text="لم يتم تحميل أي عبة")
        self.game_info_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))

        # قائمة ملفات Lua
        files_frame = ttk.LabelFrame(self.single_game_frame, text="ملفات Lua", padding="5")
        files_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # جدول الملفات
        self.files_tree = ttk.Treeview(files_frame, columns=("path", "size"), show="tree headings", height=10)
        self.files_tree.heading("#0", text="اسم الملف")
        self.files_tree.heading("path", text="المسار")
        self.files_tree.heading("size", text="الحجم")
        self.files_tree.column("#0", width=200)
        self.files_tree.column("path", width=300)
        self.files_tree.column("size", width=100)

        files_scrollbar = ttk.Scrollbar(files_frame, orient=tk.VERTICAL, command=self.files_tree.yview)
        self.files_tree.configure(yscrollcommand=files_scrollbar.set)

        self.files_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        files_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # أزرار العمليات
        buttons_frame = ttk.Frame(files_frame)
        buttons_frame.grid(row=1, column=0, columnspan=2, pady=(5, 0))

        ttk.Button(buttons_frame, text="تحديث القائمة", command=self.refresh_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="عرض الملف", command=self.view_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="نسخ احتياطية", command=self.create_backup).pack(side=tk.LEFT, padx=(0, 5))

        # منطقة العمليات
        operations_frame = ttk.LabelFrame(self.single_game_frame, text="العمليات", padding="5")
        operations_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))

        # حقن الكود
        ttk.Label(operations_frame, text="حقن كود Lua:").pack(anchor=tk.W)
        self.injection_text = scrolledtext.ScrolledText(operations_frame, height=8, width=40)
        self.injection_text.pack(fill=tk.BOTH, expand=True, pady=(5, 10))

        inject_frame = ttk.Frame(operations_frame)
        inject_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(inject_frame, text="الموضع:").pack(side=tk.LEFT)
        self.position_var = tk.StringVar(value="end")
        position_combo = ttk.Combobox(inject_frame, textvariable=self.position_var,
                                    values=["start", "end"], width=10)
        position_combo.pack(side=tk.LEFT, padx=(5, 10))

        ttk.Button(inject_frame, text="حقن الكود", command=self.inject_code).pack(side=tk.LEFT)

        # استبدال الملف
        replace_frame = ttk.Frame(operations_frame)
        replace_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(replace_frame, text="استبدال بملف", command=self.replace_file).pack(side=tk.LEFT)
        ttk.Button(replace_frame, text="استعادة النسخة الاحتياطية", command=self.restore_backup).pack(side=tk.LEFT, padx=(5, 0))

        # إعداد التوسيع للتبويب
        self.single_game_frame.columnconfigure(0, weight=1)
        self.single_game_frame.columnconfigure(1, weight=1)
        self.single_game_frame.rowconfigure(1, weight=1)
        files_frame.columnconfigure(0, weight=1)
        files_frame.rowconfigure(0, weight=1)

    def setup_search_tab(self):
        """إعداد تبويب البحث"""
        # إطار البحث
        search_frame = ttk.LabelFrame(self.search_frame, text="البحث في الألعاب", padding="5")
        search_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(search_frame, text="البحث:").grid(row=0, column=0, sticky=tk.W)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        self.search_entry.grid(row=0, column=1, padx=(5, 10))
        self.search_entry.bind('<Return>', lambda e: self.search_games())

        ttk.Button(search_frame, text="بحث", command=self.search_games).grid(row=0, column=2)
        ttk.Button(search_frame, text="تحديث قائمة الألعاب", command=self.refresh_all_games).grid(row=0, column=3, padx=(5, 0))

        # نتائج البحث
        results_frame = ttk.LabelFrame(self.search_frame, text="نتائج البحث", padding="5")
        results_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        self.search_tree = ttk.Treeview(results_frame, columns=("appid",), show="tree headings", height=15)
        self.search_tree.heading("#0", text="اسم العبة")
        self.search_tree.heading("appid", text="App ID")
        self.search_tree.column("#0", width=400)
        self.search_tree.column("appid", width=100)

        search_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.search_tree.yview)
        self.search_tree.configure(yscrollcommand=search_scrollbar.set)

        self.search_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        search_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # أزرار العمليات
        search_buttons_frame = ttk.Frame(results_frame)
        search_buttons_frame.grid(row=1, column=0, columnspan=2, pady=(5, 0))

        ttk.Button(search_buttons_frame, text="تحميل العبة المحددة", command=self.load_selected_game).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(search_buttons_frame, text="نسخ App ID", command=self.copy_app_id).pack(side=tk.LEFT)

        # إعداد التوسيع
        self.search_frame.columnconfigure(0, weight=1)
        self.search_frame.rowconfigure(1, weight=1)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)

    def setup_installed_tab(self):
        """إعداد تبويب الألعاب المثبتة"""
        # إطار التحديث
        refresh_frame = ttk.Frame(self.installed_frame)
        refresh_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(refresh_frame, text="تحديث قائمة الألعاب المثبتة", command=self.refresh_installed_games).pack(side=tk.LEFT)

        # قائمة الألعاب المثبتة
        installed_frame = ttk.LabelFrame(self.installed_frame, text="الألعاب المثبتة", padding="5")
        installed_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.installed_tree = ttk.Treeview(installed_frame, columns=("appid", "path"), show="tree headings", height=15)
        self.installed_tree.heading("#0", text="اسم العبة")
        self.installed_tree.heading("appid", text="App ID")
        self.installed_tree.heading("path", text="مسار التثبيت")
        self.installed_tree.column("#0", width=300)
        self.installed_tree.column("appid", width=100)
        self.installed_tree.column("path", width=400)

        installed_scrollbar = ttk.Scrollbar(installed_frame, orient=tk.VERTICAL, command=self.installed_tree.yview)
        self.installed_tree.configure(yscrollcommand=installed_scrollbar.set)

        self.installed_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        installed_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # أزرار العمليات
        installed_buttons_frame = ttk.Frame(installed_frame)
        installed_buttons_frame.grid(row=1, column=0, columnspan=2, pady=(5, 0))

        ttk.Button(installed_buttons_frame, text="تحميل العبة المحددة", command=self.load_selected_installed_game).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(installed_buttons_frame, text="فتح مجلد العبة", command=self.open_game_folder).pack(side=tk.LEFT)

        # إعداد التوسيع
        self.installed_frame.columnconfigure(0, weight=1)
        self.installed_frame.rowconfigure(1, weight=1)
        installed_frame.columnconfigure(0, weight=1)
        installed_frame.rowconfigure(0, weight=1)

    def setup_batch_tab(self):
        """إعداد تبويب الحقن الجماعي"""
        # تحذير
        warning_frame = ttk.LabelFrame(self.batch_frame, text="⚠️ تحذير مهم", padding="5")
        warning_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        warning_text = "الحقن الجماعي سيؤثر على جميع الألعاب المثبتة!\nتأكد من إنشاء نسخ احتياطية قبل المتابعة."
        ttk.Label(warning_frame, text=warning_text, foreground="red").pack()

        # إعدادات الحقن
        settings_frame = ttk.LabelFrame(self.batch_frame, text="إعدادات الحقن الجماعي", padding="5")
        settings_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(settings_frame, text="الموضع:").grid(row=0, column=0, sticky=tk.W)
        self.batch_position_var = tk.StringVar(value="end")
        batch_position_combo = ttk.Combobox(settings_frame, textvariable=self.batch_position_var,
                                          values=["start", "end"], width=10)
        batch_position_combo.grid(row=0, column=1, padx=(5, 10))

        # كود الحقن
        code_frame = ttk.LabelFrame(self.batch_frame, text="كود Lua للحقن", padding="5")
        code_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        self.batch_injection_text = scrolledtext.ScrolledText(code_frame, height=10, width=60)
        self.batch_injection_text.pack(fill=tk.BOTH, expand=True)

        # أزرار العمليات
        batch_buttons_frame = ttk.Frame(self.batch_frame)
        batch_buttons_frame.grid(row=3, column=0, pady=(0, 10))

        ttk.Button(batch_buttons_frame, text="تحديث قائمة الألعاب", command=self.refresh_installed_games).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(batch_buttons_frame, text="بدء الحقن الجماعي", command=self.start_batch_injection).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(batch_buttons_frame, text="تحميل كود من ملف", command=self.load_injection_code).pack(side=tk.LEFT)

        # نتائج العمليات
        results_frame = ttk.LabelFrame(self.batch_frame, text="نتائج العمليات", padding="5")
        results_frame.grid(row=4, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.batch_results_text = scrolledtext.ScrolledText(results_frame, height=8, width=60)
        self.batch_results_text.pack(fill=tk.BOTH, expand=True)

        # إعداد التوسيع
        self.batch_frame.columnconfigure(0, weight=1)
        self.batch_frame.rowconfigure(2, weight=1)
        self.batch_frame.rowconfigure(4, weight=1)

    def load_game(self):
        """تحميل العبة"""
        app_id = self.app_id_var.get().strip()
        if not app_id:
            messagebox.showerror("خطأ", "يرجى إدخال App ID")
            return
            
        self.status_var.set("جاري تحميل العبة...")
        self.root.update()
        
        if self.injector.load_game(app_id):
            game_info = self.injector.current_game
            self.game_info_label.config(text=f"العبة: {game_info['name']} | المسار: {game_info['path']}")
            self.refresh_files()
            self.status_var.set("تم تحميل العبة بنجاح")
        else:
            messagebox.showerror("خطأ", f"لم يتم العثور على العبة بـ ID: {app_id}")
            self.status_var.set("فشل في تحميل العبة")
    
    def refresh_files(self):
        """تحديث قائمة ملفات Lua"""
        # مسح القائمة الحالية
        for item in self.files_tree.get_children():
            self.files_tree.delete(item)
            
        self.current_lua_files = self.injector.get_lua_files()
        
        for lua_file in self.current_lua_files:
            size_kb = lua_file['size'] / 1024
            self.files_tree.insert("", tk.END, 
                                 text=lua_file['name'],
                                 values=(lua_file['relative_path'], f"{size_kb:.1f} KB"))
        
        self.status_var.set(f"تم العثور على {len(self.current_lua_files)} ملف Lua")
    
    def get_selected_file(self):
        """الحصول على الملف المحدد"""
        selection = self.files_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد ملف")
            return None
            
        item = self.files_tree.item(selection[0])
        file_name = item['text']
        
        for lua_file in self.current_lua_files:
            if lua_file['name'] == file_name:
                return lua_file
        return None
    
    def view_file(self):
        """عرض محتوى الملف"""
        selected_file = self.get_selected_file()
        if not selected_file:
            return
            
        content = self.injector.get_file_content(selected_file['full_path'])
        if content:
            # نافذة جديدة لعرض المحتوى
            view_window = tk.Toplevel(self.root)
            view_window.title(f"عرض: {selected_file['name']}")
            view_window.geometry("600x400")
            
            text_widget = scrolledtext.ScrolledText(view_window)
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            text_widget.insert(tk.END, content)
            text_widget.config(state=tk.DISABLED)
    
    def inject_code(self):
        """حقن الكود"""
        selected_file = self.get_selected_file()
        if not selected_file:
            return
            
        code = self.injection_text.get(1.0, tk.END).strip()
        if not code:
            messagebox.showwarning("تحذير", "يرجى إدخال الكود المراد حقنه")
            return
            
        position = self.position_var.get()
        
        if self.injector.inject_code(selected_file['full_path'], code, position):
            messagebox.showinfo("نجح", "تم حقن الكود بنجاح")
            self.status_var.set("تم حقن الكود")
        else:
            messagebox.showerror("خطأ", "فشل في حقن الكود")
    
    def replace_file(self):
        """استبدال الملف"""
        selected_file = self.get_selected_file()
        if not selected_file:
            return
            
        new_file = filedialog.askopenfilename(
            title="اختر ملف Lua جديد",
            filetypes=[("Lua files", "*.lua"), ("All files", "*.*")]
        )
        
        if new_file:
            if self.injector.replace_file(selected_file['full_path'], new_file):
                messagebox.showinfo("نجح", "تم استبدال الملف بنجاح")
                self.status_var.set("تم استبدال الملف")
            else:
                messagebox.showerror("خطأ", "فشل في استبدال الملف")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        selected_file = self.get_selected_file()
        if not selected_file:
            return
            
        if self.injector.create_backup(selected_file['full_path']):
            messagebox.showinfo("نجح", "تم إنشاء النسخة الاحتياطية")
            self.status_var.set("تم إنشاء النسخة الاحتياطية")
        else:
            messagebox.showerror("خطأ", "فشل في إنشاء النسخة الاحتياطية")
    
    def restore_backup(self):
        """استعادة النسخة الاحتياطية"""
        selected_file = self.get_selected_file()
        if not selected_file:
            return
            
        if self.injector.restore_backup(selected_file['full_path']):
            messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية")
            self.status_var.set("تم استعادة النسخة الاحتياطية")
        else:
            messagebox.showerror("خطأ", "فشل في استعادة النسخة الاحتياطية")

    # وظائف التبويبات الجديدة
    def search_games(self):
        """البحث في الألعاب"""
        query = self.search_var.get().strip()
        self.status_var.set("جاري البحث...")
        self.root.update()

        # مسح النتائج السابقة
        for item in self.search_tree.get_children():
            self.search_tree.delete(item)

        try:
            results = self.injector.search_games(query, limit=100)

            for game in results:
                self.search_tree.insert("", tk.END,
                                      text=game['name'],
                                      values=(game['appid'],))

            self.status_var.set(f"تم العثور على {len(results)} نتيجة")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {e}")
            self.status_var.set("فشل في البحث")

    def refresh_all_games(self):
        """تحديث قائمة جميع الألعاب"""
        self.status_var.set("جاري تحديث قائمة الألعاب...")
        self.root.update()

        try:
            self.injector.get_all_steam_games(force_refresh=True)
            self.status_var.set("تم تحديث قائمة الألعاب")
            messagebox.showinfo("نجح", "تم تحديث قائمة الألعاب بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث القائمة: {e}")
            self.status_var.set("فشل في التحديث")

    def load_selected_game(self):
        """تحميل العبة المحددة من نتائج البحث"""
        selection = self.search_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد عبة")
            return

        item = self.search_tree.item(selection[0])
        app_id = item['values'][0]

        # تحديث App ID في التبويب الأول
        self.app_id_var.set(str(app_id))

        # التبديل إلى التبويب الأول
        self.notebook.select(0)

        # تحميل العبة
        self.load_game()

    def copy_app_id(self):
        """نسخ App ID إلى الحافظة"""
        selection = self.search_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد عبة")
            return

        item = self.search_tree.item(selection[0])
        app_id = item['values'][0]

        self.root.clipboard_clear()
        self.root.clipboard_append(str(app_id))
        messagebox.showinfo("نجح", f"تم نسخ App ID: {app_id}")

    def refresh_installed_games(self):
        """تحديث قائمة الألعاب المثبتة"""
        self.status_var.set("جاري البحث عن الألعاب المثبتة...")
        self.root.update()

        # مسح القائمة الحالية
        for item in self.installed_tree.get_children():
            self.installed_tree.delete(item)

        try:
            self.installed_games = self.injector.get_installed_games(force_refresh=True)

            for game in self.installed_games:
                self.installed_tree.insert("", tk.END,
                                         text=game['name'],
                                         values=(game['appid'], game['path']))

            self.status_var.set(f"تم العثور على {len(self.installed_games)} لعبة مثبتة")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في جلب الألعاب المثبتة: {e}")
            self.status_var.set("فشل في جلب الألعاب المثبتة")

    def load_selected_installed_game(self):
        """تحميل العبة المحددة من الألعاب المثبتة"""
        selection = self.installed_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد عبة")
            return

        item = self.installed_tree.item(selection[0])
        app_id = item['values'][0]

        # تحديث App ID في التبويب الأول
        self.app_id_var.set(str(app_id))

        # التبديل إلى التبويب الأول
        self.notebook.select(0)

        # تحميل العبة
        self.load_game()

    def open_game_folder(self):
        """فتح مجلد العبة"""
        selection = self.installed_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد عبة")
            return

        item = self.installed_tree.item(selection[0])
        game_path = item['values'][1]

        try:
            os.startfile(game_path)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح المجلد: {e}")

    def start_batch_injection(self):
        """بدء الحقن الجماعي"""
        code = self.batch_injection_text.get(1.0, tk.END).strip()
        if not code:
            messagebox.showwarning("تحذير", "يرجى إدخال كود Lua للحقن")
            return

        position = self.batch_position_var.get()

        # تأكيد من المستخدم
        response = messagebox.askyesno(
            "تأكيد",
            "هل أنت متأكد من بدء الحقن الجماعي؟\nسيتم حقن الكود في جميع الألعاب المثبتة!"
        )

        if not response:
            return

        self.status_var.set("جاري الحقن الجماعي...")
        self.root.update()

        # مسح النتائج السابقة
        self.batch_results_text.delete(1.0, tk.END)

        try:
            results = self.injector.inject_all_games(code, position)

            # عرض النتائج
            total_games = len(results)
            total_files = sum(r['files_processed'] for r in results)
            total_errors = sum(r['files_failed'] for r in results)

            summary = f"=== ملخص الحقن الجماعي ===\n"
            summary += f"إجمالي الألعاب: {total_games}\n"
            summary += f"إجمالي الملفات المعالجة: {total_files}\n"
            summary += f"إجمالي الأخطاء: {total_errors}\n\n"

            self.batch_results_text.insert(tk.END, summary)

            for result in results:
                game_result = f"العبة: {result['game']} (ID: {result['appid']})\n"
                game_result += f"  ملفات معالجة: {result['files_processed']}\n"
                game_result += f"  أخطاء: {result['files_failed']}\n"

                if result['errors']:
                    game_result += f"  تفاصيل الأخطاء:\n"
                    for error in result['errors']:
                        game_result += f"    - {error}\n"

                game_result += "\n"
                self.batch_results_text.insert(tk.END, game_result)

            self.status_var.set(f"تم الحقن الجماعي: {total_files} ملف، {total_errors} خطأ")
            messagebox.showinfo("انتهى", f"تم الحقن الجماعي!\nملفات معالجة: {total_files}\nأخطاء: {total_errors}")

        except Exception as e:
            error_msg = f"خطأ في الحقن الجماعي: {e}"
            self.batch_results_text.insert(tk.END, error_msg)
            messagebox.showerror("خطأ", error_msg)
            self.status_var.set("فشل في الحقن الجماعي")

    def load_injection_code(self):
        """تحميل كود الحقن من ملف"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف كود Lua",
            filetypes=[("Lua files", "*.lua"), ("Text files", "*.txt"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    code = f.read()

                self.batch_injection_text.delete(1.0, tk.END)
                self.batch_injection_text.insert(1.0, code)

                self.status_var.set(f"تم تحميل الكود من: {os.path.basename(file_path)}")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل الملف: {e}")

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SteamInjectorGUI()
    app.run()
