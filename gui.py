"""
واجهة المستخدم الرسومية لمراقب الشبكة
"""
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from network_scanner import NetworkScanner
from bandwidth_monitor import BandwidthMonitor
from speed_tester import SpeedTester
from device_manager import DeviceManager

class NetworkMonitorGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("مراقب ومدير الشبكة")
        self.root.geometry("1200x800")
        
        # إنشاء المكونات الأساسية
        self.scanner = NetworkScanner()
        self.bandwidth_monitor = BandwidthMonitor()
        self.speed_tester = SpeedTester()
        self.device_manager = DeviceManager()
        
        # متغيرات الواجهة
        self.devices_data = {}
        self.monitoring_active = False
        
        self.setup_ui()
        
        # بدء مراقبة البيانات
        self.start_bandwidth_monitoring()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # إنشاء تبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # التبويبات
        self.setup_devices_tab()
        self.setup_bandwidth_tab()
        self.setup_speed_tab()
        self.setup_management_tab()
        
        # شريط الحالة
        self.status_var = tk.StringVar(value="جاهز")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # إعداد التوسيع
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(0, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def setup_devices_tab(self):
        """إعداد تبويب الأجهزة"""
        self.devices_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.devices_frame, text="الأجهزة المتصلة")
        
        # أزرار التحكم
        control_frame = ttk.Frame(self.devices_frame)
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(control_frame, text="فحص الشبكة", command=self.scan_network).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="تحديث", command=self.refresh_devices).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="إيقاف الفحص", command=self.stop_scan).pack(side=tk.LEFT)
        
        # معلومات الشبكة
        info_frame = ttk.LabelFrame(self.devices_frame, text="معلومات الشبكة", padding="5")
        info_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.network_info_text = tk.Text(info_frame, height=4, width=50)
        self.network_info_text.pack(fill=tk.X)
        
        # جدول الأجهزة
        devices_frame = ttk.LabelFrame(self.devices_frame, text="الأجهزة المكتشفة", padding="5")
        devices_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # إنشاء Treeview للأجهزة
        columns = ("ip", "mac", "name", "vendor", "status")
        self.devices_tree = ttk.Treeview(devices_frame, columns=columns, show="tree headings", height=15)
        
        # تحديد عناوين الأعمدة
        self.devices_tree.heading("#0", text="الجهاز")
        self.devices_tree.heading("ip", text="IP Address")
        self.devices_tree.heading("mac", text="MAC Address")
        self.devices_tree.heading("name", text="اسم الجهاز")
        self.devices_tree.heading("vendor", text="الشركة المصنعة")
        self.devices_tree.heading("status", text="الحالة")
        
        # تحديد عرض الأعمدة
        self.devices_tree.column("#0", width=100)
        self.devices_tree.column("ip", width=120)
        self.devices_tree.column("mac", width=150)
        self.devices_tree.column("name", width=150)
        self.devices_tree.column("vendor", width=120)
        self.devices_tree.column("status", width=100)
        
        # شريط التمرير
        devices_scrollbar = ttk.Scrollbar(devices_frame, orient=tk.VERTICAL, command=self.devices_tree.yview)
        self.devices_tree.configure(yscrollcommand=devices_scrollbar.set)
        
        self.devices_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        devices_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # إعداد التوسيع
        self.devices_frame.columnconfigure(0, weight=1)
        self.devices_frame.rowconfigure(2, weight=1)
        devices_frame.columnconfigure(0, weight=1)
        devices_frame.rowconfigure(0, weight=1)
    
    def setup_bandwidth_tab(self):
        """إعداد تبويب مراقبة البيانات"""
        self.bandwidth_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.bandwidth_frame, text="مراقبة البيانات")
        
        # أزرار التحكم
        control_frame = ttk.Frame(self.bandwidth_frame)
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(control_frame, text="بدء المراقبة", command=self.start_monitoring).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="إيقاف المراقبة", command=self.stop_monitoring).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="إعادة تعيين البيانات", command=self.reset_bandwidth_data).pack(side=tk.LEFT)
        
        # ملخص الاستخدام
        summary_frame = ttk.LabelFrame(self.bandwidth_frame, text="ملخص الاستخدام", padding="5")
        summary_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.bandwidth_summary_text = scrolledtext.ScrolledText(summary_frame, height=8, width=60)
        self.bandwidth_summary_text.pack(fill=tk.BOTH, expand=True)
        
        # تفاصيل الواجهات
        interfaces_frame = ttk.LabelFrame(self.bandwidth_frame, text="واجهات الشبكة", padding="5")
        interfaces_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # جدول الواجهات
        interface_columns = ("interface", "download", "upload", "total", "speed_down", "speed_up")
        self.interfaces_tree = ttk.Treeview(interfaces_frame, columns=interface_columns, show="tree headings", height=10)
        
        self.interfaces_tree.heading("#0", text="الواجهة")
        self.interfaces_tree.heading("interface", text="اسم الواجهة")
        self.interfaces_tree.heading("download", text="إجمالي التحميل")
        self.interfaces_tree.heading("upload", text="إجمالي الرفع")
        self.interfaces_tree.heading("total", text="إجمالي البيانات")
        self.interfaces_tree.heading("speed_down", text="سرعة التحميل")
        self.interfaces_tree.heading("speed_up", text="سرعة الرفع")
        
        interfaces_scrollbar = ttk.Scrollbar(interfaces_frame, orient=tk.VERTICAL, command=self.interfaces_tree.yview)
        self.interfaces_tree.configure(yscrollcommand=interfaces_scrollbar.set)
        
        self.interfaces_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        interfaces_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # إعداد التوسيع
        self.bandwidth_frame.columnconfigure(0, weight=1)
        self.bandwidth_frame.rowconfigure(2, weight=1)
        interfaces_frame.columnconfigure(0, weight=1)
        interfaces_frame.rowconfigure(0, weight=1)
    
    def setup_speed_tab(self):
        """إعداد تبويب اختبار السرعة"""
        self.speed_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.speed_frame, text="اختبار السرعة")
        
        # أزرار الاختبار
        test_frame = ttk.LabelFrame(self.speed_frame, text="اختبارات السرعة", padding="5")
        test_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(test_frame, text="اختبار سرعة الإنترنت", command=self.test_internet_speed).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(test_frame, text="اختبار DNS", command=self.test_dns_speed).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(test_frame, text="اختبار المواقع", command=self.test_websites_speed).pack(side=tk.LEFT)
        
        # نتائج الاختبارات
        results_frame = ttk.LabelFrame(self.speed_frame, text="نتائج الاختبارات", padding="5")
        results_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.speed_results_text = scrolledtext.ScrolledText(results_frame, height=20, width=80)
        self.speed_results_text.pack(fill=tk.BOTH, expand=True)
        
        # إعداد التوسيع
        self.speed_frame.columnconfigure(0, weight=1)
        self.speed_frame.rowconfigure(1, weight=1)
    
    def setup_management_tab(self):
        """إعداد تبويب إدارة الأجهزة"""
        self.management_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.management_frame, text="إدارة الأجهزة")
        
        # قسم الحظر
        block_frame = ttk.LabelFrame(self.management_frame, text="حظر الأجهزة", padding="5")
        block_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # إدخال IP للحظر
        ttk.Label(block_frame, text="IP Address:").grid(row=0, column=0, sticky=tk.W)
        self.block_ip_var = tk.StringVar()
        self.block_ip_entry = ttk.Entry(block_frame, textvariable=self.block_ip_var, width=20)
        self.block_ip_entry.grid(row=0, column=1, padx=(5, 10))
        
        ttk.Button(block_frame, text="حظر", command=self.block_device).grid(row=0, column=2, padx=(0, 5))
        ttk.Button(block_frame, text="إلغاء الحظر", command=self.unblock_device).grid(row=0, column=3)
        
        # قائمة الأجهزة المحظورة
        blocked_frame = ttk.LabelFrame(self.management_frame, text="الأجهزة المحظورة", padding="5")
        blocked_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.blocked_devices_listbox = tk.Listbox(blocked_frame, height=10)
        self.blocked_devices_listbox.pack(fill=tk.BOTH, expand=True)
        
        # أزرار إدارة الحظر
        blocked_buttons_frame = ttk.Frame(blocked_frame)
        blocked_buttons_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(blocked_buttons_frame, text="تحديث القائمة", command=self.refresh_blocked_devices).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(blocked_buttons_frame, text="إلغاء حظر المحدد", command=self.unblock_selected).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(blocked_buttons_frame, text="إلغاء حظر الكل", command=self.unblock_all).pack(side=tk.LEFT)
        
        # إعداد التوسيع
        self.management_frame.columnconfigure(0, weight=1)
        self.management_frame.rowconfigure(1, weight=1)

    # وظائف تبويب الأجهزة
    def scan_network(self):
        """بدء فحص الشبكة"""
        self.status_var.set("جاري فحص الشبكة...")

        def scan_callback(device):
            # تحديث الواجهة عند اكتشاف جهاز جديد
            self.root.after(0, lambda: self.add_device_to_tree(device))

        def run_scan():
            try:
                # مسح الجدول الحالي
                self.root.after(0, self.clear_devices_tree)

                # تحديث معلومات الشبكة
                network_info = self.scanner.get_network_info()
                info_text = f"IP المحلي: {network_info['local_ip']}\n"
                info_text += f"Gateway: {network_info['gateway']}\n"
                info_text += f"نطاق الشبكة: {network_info['network_range']}\n"
                info_text += f"الواجهة: {network_info['interface']}"

                self.root.after(0, lambda: self.update_network_info(info_text))

                # بدء الفحص
                devices = self.scanner.scan_network(scan_callback)

                self.root.after(0, lambda: self.status_var.set(f"تم العثور على {len(devices)} جهاز"))

            except Exception as e:
                self.root.after(0, lambda: self.status_var.set(f"خطأ في الفحص: {e}"))

        # تشغيل الفحص في خيط منفصل
        scan_thread = threading.Thread(target=run_scan)
        scan_thread.daemon = True
        scan_thread.start()

    def stop_scan(self):
        """إيقاف فحص الشبكة"""
        self.scanner.stop_scan()
        self.status_var.set("تم إيقاف الفحص")

    def refresh_devices(self):
        """تحديث قائمة الأجهزة"""
        self.clear_devices_tree()
        for ip, device in self.scanner.devices.items():
            self.add_device_to_tree(device)

    def clear_devices_tree(self):
        """مسح جدول الأجهزة"""
        for item in self.devices_tree.get_children():
            self.devices_tree.delete(item)

    def add_device_to_tree(self, device):
        """إضافة جهاز إلى الجدول"""
        status = "محظور" if self.device_manager.is_device_blocked(device['ip']) else device['status']
        vendor = self.scanner.get_device_vendor(device['mac'])

        self.devices_tree.insert("", tk.END,
                               text=device['name'],
                               values=(device['ip'], device['mac'], device['name'], vendor, status))

    def update_network_info(self, info_text):
        """تحديث معلومات الشبكة"""
        self.network_info_text.delete(1.0, tk.END)
        self.network_info_text.insert(1.0, info_text)

    # وظائف تبويب مراقبة البيانات
    def start_bandwidth_monitoring(self):
        """بدء مراقبة البيانات"""
        def bandwidth_callback(usage_data):
            self.root.after(0, lambda: self.update_bandwidth_display(usage_data))

        self.bandwidth_monitor.start_monitoring(bandwidth_callback, interval=2)
        self.monitoring_active = True

    def start_monitoring(self):
        """بدء مراقبة البيانات (زر الواجهة)"""
        if not self.monitoring_active:
            self.start_bandwidth_monitoring()
            self.status_var.set("بدأت مراقبة البيانات")

    def stop_monitoring(self):
        """إيقاف مراقبة البيانات"""
        self.bandwidth_monitor.stop_monitoring()
        self.monitoring_active = False
        self.status_var.set("تم إيقاف مراقبة البيانات")

    def reset_bandwidth_data(self):
        """إعادة تعيين بيانات الاستخدام"""
        response = messagebox.askyesno("تأكيد", "هل تريد إعادة تعيين جميع بيانات الاستخدام؟")
        if response:
            self.bandwidth_monitor.reset_usage_data()
            self.update_bandwidth_summary()
            self.status_var.set("تم إعادة تعيين بيانات الاستخدام")

    def update_bandwidth_display(self, usage_data):
        """تحديث عرض البيانات"""
        # تحديث جدول الواجهات
        for item in self.interfaces_tree.get_children():
            self.interfaces_tree.delete(item)

        for interface, data in usage_data.items():
            download_speed = self.bandwidth_monitor.format_speed(data['download_speed'])
            upload_speed = self.bandwidth_monitor.format_speed(data['upload_speed'])
            total_download = self.bandwidth_monitor.format_bytes(data['total_download'])
            total_upload = self.bandwidth_monitor.format_bytes(data['total_upload'])
            total_data = self.bandwidth_monitor.format_bytes(data['total_download'] + data['total_upload'])

            self.interfaces_tree.insert("", tk.END,
                                      text=interface,
                                      values=(interface, total_download, total_upload, total_data, download_speed, upload_speed))

        # تحديث الملخص
        self.update_bandwidth_summary()

    def update_bandwidth_summary(self):
        """تحديث ملخص الاستخدام"""
        summaries = self.bandwidth_monitor.get_all_interfaces_summary()

        summary_text = "=== ملخص استهلاك البيانات ===\n\n"

        for interface, summary in summaries.items():
            summary_text += f"الواجهة: {interface}\n"
            summary_text += f"إجمالي التحميل: {summary['total_download']}\n"
            summary_text += f"إجمالي الرفع: {summary['total_upload']}\n"
            summary_text += f"إجمالي البيانات: {summary['total_usage']}\n"
            summary_text += f"سرعة التحميل الحالية: {summary['current_download_speed']}\n"
            summary_text += f"سرعة الرفع الحالية: {summary['current_upload_speed']}\n"
            summary_text += f"أقصى سرعة تحميل: {summary['peak_download_speed']}\n"
            summary_text += f"أقصى سرعة رفع: {summary['peak_upload_speed']}\n"
            if 'session_time' in summary:
                summary_text += f"وقت الجلسة: {summary['session_time']}\n"
            summary_text += "\n" + "="*50 + "\n\n"

        self.bandwidth_summary_text.delete(1.0, tk.END)
        self.bandwidth_summary_text.insert(1.0, summary_text)

    # وظائف تبويب اختبار السرعة
    def test_internet_speed(self):
        """اختبار سرعة الإنترنت"""
        self.status_var.set("جاري اختبار سرعة الإنترنت...")

        def speed_callback(message):
            self.root.after(0, lambda: self.status_var.set(message))

        def run_test():
            try:
                results = self.speed_tester.test_internet_speed(speed_callback)
                self.root.after(0, lambda: self.display_speed_results("اختبار سرعة الإنترنت", results))
            except Exception as e:
                self.root.after(0, lambda: self.status_var.set(f"خطأ في اختبار السرعة: {e}"))

        test_thread = threading.Thread(target=run_test)
        test_thread.daemon = True
        test_thread.start()

    def test_dns_speed(self):
        """اختبار سرعة DNS"""
        self.status_var.set("جاري اختبار سرعة DNS...")

        def run_test():
            try:
                results = self.speed_tester.test_dns_speed()
                self.root.after(0, lambda: self.display_speed_results("اختبار سرعة DNS", results))
            except Exception as e:
                self.root.after(0, lambda: self.status_var.set(f"خطأ في اختبار DNS: {e}"))

        test_thread = threading.Thread(target=run_test)
        test_thread.daemon = True
        test_thread.start()

    def test_websites_speed(self):
        """اختبار سرعة المواقع"""
        self.status_var.set("جاري اختبار سرعة المواقع...")

        def run_test():
            try:
                results = self.speed_tester.test_website_speed()
                self.root.after(0, lambda: self.display_speed_results("اختبار سرعة المواقع", results))
            except Exception as e:
                self.root.after(0, lambda: self.status_var.set(f"خطأ في اختبار المواقع: {e}"))

        test_thread = threading.Thread(target=run_test)
        test_thread.daemon = True
        test_thread.start()

    def display_speed_results(self, test_name, results):
        """عرض نتائج اختبار السرعة"""
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        result_text = f"\n{'='*60}\n"
        result_text += f"{test_name} - {current_time}\n"
        result_text += f"{'='*60}\n"

        if 'error' in results:
            result_text += f"خطأ: {results['error']}\n"
        elif test_name == "اختبار سرعة الإنترنت":
            if 'download_speed' in results:
                result_text += f"سرعة التحميل: {self.speed_tester.format_speed(results['download_speed'])}\n"
                result_text += f"سرعة الرفع: {self.speed_tester.format_speed(results['upload_speed'])}\n"
                result_text += f"زمن الاستجابة: {results['ping']:.2f} ms\n"
                result_text += f"تقييم السرعة: {self.speed_tester.get_speed_rating(results['download_speed'])}\n"
                if 'server' in results:
                    server = results['server']
                    result_text += f"الخادم: {server.get('sponsor', 'غير معروف')} - {server.get('name', 'غير معروف')}\n"
        elif test_name == "اختبار سرعة DNS":
            result_text += "نتائج خوادم DNS:\n"
            for dns, response_time in results.items():
                if response_time > 0:
                    result_text += f"  {dns}: {response_time:.2f} ms\n"
                else:
                    result_text += f"  {dns}: فشل في الاتصال\n"
        elif test_name == "اختبار سرعة المواقع":
            result_text += "نتائج المواقع:\n"
            for url, data in results.items():
                if data['success']:
                    result_text += f"  {url}: {data['response_time']:.2f} ms\n"
                else:
                    result_text += f"  {url}: فشل ({data.get('error', 'خطأ غير معروف')})\n"

        result_text += f"{'='*60}\n"

        # إضافة النتائج إلى النص
        self.speed_results_text.insert(tk.END, result_text)
        self.speed_results_text.see(tk.END)

        self.status_var.set(f"تم الانتهاء من {test_name}")

    # وظائف تبويب إدارة الأجهزة
    def block_device(self):
        """حظر جهاز"""
        ip = self.block_ip_var.get().strip()
        if not ip:
            messagebox.showwarning("تحذير", "يرجى إدخال IP Address")
            return

        self.status_var.set(f"جاري حظر الجهاز {ip}...")

        def run_block():
            try:
                success = self.device_manager.block_device_by_ip(ip)
                if success:
                    self.root.after(0, lambda: self.status_var.set(f"تم حظر الجهاز {ip}"))
                    self.root.after(0, self.refresh_blocked_devices)
                    self.root.after(0, self.refresh_devices)  # تحديث حالة الجهاز في قائمة الأجهزة
                else:
                    self.root.after(0, lambda: self.status_var.set(f"فشل في حظر الجهاز {ip}"))
            except Exception as e:
                self.root.after(0, lambda: self.status_var.set(f"خطأ في حظر الجهاز: {e}"))

        block_thread = threading.Thread(target=run_block)
        block_thread.daemon = True
        block_thread.start()

    def unblock_device(self):
        """إلغاء حظر جهاز"""
        ip = self.block_ip_var.get().strip()
        if not ip:
            messagebox.showwarning("تحذير", "يرجى إدخال IP Address")
            return

        self.status_var.set(f"جاري إلغاء حظر الجهاز {ip}...")

        def run_unblock():
            try:
                success = self.device_manager.unblock_device(ip)
                if success:
                    self.root.after(0, lambda: self.status_var.set(f"تم إلغاء حظر الجهاز {ip}"))
                    self.root.after(0, self.refresh_blocked_devices)
                    self.root.after(0, self.refresh_devices)
                else:
                    self.root.after(0, lambda: self.status_var.set(f"فشل في إلغاء حظر الجهاز {ip}"))
            except Exception as e:
                self.root.after(0, lambda: self.status_var.set(f"خطأ في إلغاء حظر الجهاز: {e}"))

        unblock_thread = threading.Thread(target=run_unblock)
        unblock_thread.daemon = True
        unblock_thread.start()

    def refresh_blocked_devices(self):
        """تحديث قائمة الأجهزة المحظورة"""
        self.blocked_devices_listbox.delete(0, tk.END)
        blocked_devices = self.device_manager.get_blocked_devices()
        for ip in blocked_devices:
            self.blocked_devices_listbox.insert(tk.END, ip)

    def unblock_selected(self):
        """إلغاء حظر الجهاز المحدد"""
        selection = self.blocked_devices_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد جهاز من القائمة")
            return

        ip = self.blocked_devices_listbox.get(selection[0])
        self.block_ip_var.set(ip)
        self.unblock_device()

    def unblock_all(self):
        """إلغاء حظر جميع الأجهزة"""
        blocked_devices = self.device_manager.get_blocked_devices()
        if not blocked_devices:
            messagebox.showinfo("معلومات", "لا توجد أجهزة محظورة")
            return

        response = messagebox.askyesno("تأكيد", f"هل تريد إلغاء حظر جميع الأجهزة ({len(blocked_devices)} جهاز)؟")
        if response:
            self.status_var.set("جاري إلغاء حظر جميع الأجهزة...")

            def run_unblock_all():
                try:
                    results = self.device_manager.unblock_multiple_devices(blocked_devices)
                    success_count = sum(1 for success in results.values() if success)

                    self.root.after(0, lambda: self.status_var.set(f"تم إلغاء حظر {success_count} من {len(blocked_devices)} جهاز"))
                    self.root.after(0, self.refresh_blocked_devices)
                    self.root.after(0, self.refresh_devices)
                except Exception as e:
                    self.root.after(0, lambda: self.status_var.set(f"خطأ في إلغاء الحظر: {e}"))

            unblock_thread = threading.Thread(target=run_unblock_all)
            unblock_thread.daemon = True
            unblock_thread.start()

    def run(self):
        """تشغيل التطبيق"""
        # تحديث قائمة الأجهزة المحظورة عند البدء
        self.refresh_blocked_devices()

        # بدء الحلقة الرئيسية
        self.root.mainloop()

        # إيقاف المراقبة عند الإغلاق
        self.bandwidth_monitor.stop_monitoring()

if __name__ == "__main__":
    app = NetworkMonitorGUI()
    app.run()
