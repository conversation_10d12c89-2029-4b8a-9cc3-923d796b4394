"""
واجهة المستخدم الرسومية لـ Steam Lua Injector
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
from steam_injector import SteamInjector

class SteamInjectorGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Steam Lua Injector")
        self.root.geometry("800x600")
        self.root.direction = "rtl"  # للدعم العربي
        
        self.injector = SteamInjector()
        self.current_lua_files = []
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # إعداد العبة
        game_frame = ttk.LabelFrame(main_frame, text="إعدادات العبة", padding="5")
        game_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(game_frame, text="App ID:").grid(row=0, column=0, sticky=tk.W)
        self.app_id_var = tk.StringVar()
        self.app_id_entry = ttk.Entry(game_frame, textvariable=self.app_id_var, width=20)
        self.app_id_entry.grid(row=0, column=1, padx=(5, 10))
        
        self.load_game_btn = ttk.Button(game_frame, text="تحميل العبة", command=self.load_game)
        self.load_game_btn.grid(row=0, column=2)
        
        self.game_info_label = ttk.Label(game_frame, text="لم يتم تحميل أي عبة")
        self.game_info_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))
        
        # قائمة ملفات Lua
        files_frame = ttk.LabelFrame(main_frame, text="ملفات Lua", padding="5")
        files_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # جدول الملفات
        self.files_tree = ttk.Treeview(files_frame, columns=("path", "size"), show="tree headings", height=10)
        self.files_tree.heading("#0", text="اسم الملف")
        self.files_tree.heading("path", text="المسار")
        self.files_tree.heading("size", text="الحجم")
        self.files_tree.column("#0", width=200)
        self.files_tree.column("path", width=300)
        self.files_tree.column("size", width=100)
        
        files_scrollbar = ttk.Scrollbar(files_frame, orient=tk.VERTICAL, command=self.files_tree.yview)
        self.files_tree.configure(yscrollcommand=files_scrollbar.set)
        
        self.files_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        files_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(files_frame)
        buttons_frame.grid(row=1, column=0, columnspan=2, pady=(5, 0))
        
        ttk.Button(buttons_frame, text="تحديث القائمة", command=self.refresh_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="عرض الملف", command=self.view_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="نسخ احتياطية", command=self.create_backup).pack(side=tk.LEFT, padx=(0, 5))
        
        # منطقة العمليات
        operations_frame = ttk.LabelFrame(main_frame, text="العمليات", padding="5")
        operations_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))
        
        # حقن الكود
        ttk.Label(operations_frame, text="حقن كود Lua:").pack(anchor=tk.W)
        self.injection_text = scrolledtext.ScrolledText(operations_frame, height=8, width=40)
        self.injection_text.pack(fill=tk.BOTH, expand=True, pady=(5, 10))
        
        inject_frame = ttk.Frame(operations_frame)
        inject_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(inject_frame, text="الموضع:").pack(side=tk.LEFT)
        self.position_var = tk.StringVar(value="end")
        position_combo = ttk.Combobox(inject_frame, textvariable=self.position_var, 
                                    values=["start", "end"], width=10)
        position_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Button(inject_frame, text="حقن الكود", command=self.inject_code).pack(side=tk.LEFT)
        
        # استبدال الملف
        replace_frame = ttk.Frame(operations_frame)
        replace_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(replace_frame, text="استبدال بملف", command=self.replace_file).pack(side=tk.LEFT)
        ttk.Button(replace_frame, text="استعادة النسخة الاحتياطية", command=self.restore_backup).pack(side=tk.LEFT, padx=(5, 0))
        
        # شريط الحالة
        self.status_var = tk.StringVar(value="جاهز")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # إعداد التوسيع
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        files_frame.columnconfigure(0, weight=1)
        files_frame.rowconfigure(0, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
    def load_game(self):
        """تحميل العبة"""
        app_id = self.app_id_var.get().strip()
        if not app_id:
            messagebox.showerror("خطأ", "يرجى إدخال App ID")
            return
            
        self.status_var.set("جاري تحميل العبة...")
        self.root.update()
        
        if self.injector.load_game(app_id):
            game_info = self.injector.current_game
            self.game_info_label.config(text=f"العبة: {game_info['name']} | المسار: {game_info['path']}")
            self.refresh_files()
            self.status_var.set("تم تحميل العبة بنجاح")
        else:
            messagebox.showerror("خطأ", f"لم يتم العثور على العبة بـ ID: {app_id}")
            self.status_var.set("فشل في تحميل العبة")
    
    def refresh_files(self):
        """تحديث قائمة ملفات Lua"""
        # مسح القائمة الحالية
        for item in self.files_tree.get_children():
            self.files_tree.delete(item)
            
        self.current_lua_files = self.injector.get_lua_files()
        
        for lua_file in self.current_lua_files:
            size_kb = lua_file['size'] / 1024
            self.files_tree.insert("", tk.END, 
                                 text=lua_file['name'],
                                 values=(lua_file['relative_path'], f"{size_kb:.1f} KB"))
        
        self.status_var.set(f"تم العثور على {len(self.current_lua_files)} ملف Lua")
    
    def get_selected_file(self):
        """الحصول على الملف المحدد"""
        selection = self.files_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد ملف")
            return None
            
        item = self.files_tree.item(selection[0])
        file_name = item['text']
        
        for lua_file in self.current_lua_files:
            if lua_file['name'] == file_name:
                return lua_file
        return None
    
    def view_file(self):
        """عرض محتوى الملف"""
        selected_file = self.get_selected_file()
        if not selected_file:
            return
            
        content = self.injector.get_file_content(selected_file['full_path'])
        if content:
            # نافذة جديدة لعرض المحتوى
            view_window = tk.Toplevel(self.root)
            view_window.title(f"عرض: {selected_file['name']}")
            view_window.geometry("600x400")
            
            text_widget = scrolledtext.ScrolledText(view_window)
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            text_widget.insert(tk.END, content)
            text_widget.config(state=tk.DISABLED)
    
    def inject_code(self):
        """حقن الكود"""
        selected_file = self.get_selected_file()
        if not selected_file:
            return
            
        code = self.injection_text.get(1.0, tk.END).strip()
        if not code:
            messagebox.showwarning("تحذير", "يرجى إدخال الكود المراد حقنه")
            return
            
        position = self.position_var.get()
        
        if self.injector.inject_code(selected_file['full_path'], code, position):
            messagebox.showinfo("نجح", "تم حقن الكود بنجاح")
            self.status_var.set("تم حقن الكود")
        else:
            messagebox.showerror("خطأ", "فشل في حقن الكود")
    
    def replace_file(self):
        """استبدال الملف"""
        selected_file = self.get_selected_file()
        if not selected_file:
            return
            
        new_file = filedialog.askopenfilename(
            title="اختر ملف Lua جديد",
            filetypes=[("Lua files", "*.lua"), ("All files", "*.*")]
        )
        
        if new_file:
            if self.injector.replace_file(selected_file['full_path'], new_file):
                messagebox.showinfo("نجح", "تم استبدال الملف بنجاح")
                self.status_var.set("تم استبدال الملف")
            else:
                messagebox.showerror("خطأ", "فشل في استبدال الملف")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        selected_file = self.get_selected_file()
        if not selected_file:
            return
            
        if self.injector.create_backup(selected_file['full_path']):
            messagebox.showinfo("نجح", "تم إنشاء النسخة الاحتياطية")
            self.status_var.set("تم إنشاء النسخة الاحتياطية")
        else:
            messagebox.showerror("خطأ", "فشل في إنشاء النسخة الاحتياطية")
    
    def restore_backup(self):
        """استعادة النسخة الاحتياطية"""
        selected_file = self.get_selected_file()
        if not selected_file:
            return
            
        if self.injector.restore_backup(selected_file['full_path']):
            messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية")
            self.status_var.set("تم استعادة النسخة الاحتياطية")
        else:
            messagebox.showerror("خطأ", "فشل في استعادة النسخة الاحتياطية")
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SteamInjectorGUI()
    app.run()
