@echo off
echo ========================================
echo    📦 تثبيت متطلبات Steam Depot Downloader Pro
echo ========================================
echo.

echo 🔍 التحقق من Python...
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python من: https://python.org
    pause
    exit /b
)

echo ✅ تم العثور على Python
echo.

echo 📦 تثبيت المتطلبات...
pip install -r requirements.txt

if %errorLevel% equ 0 (
    echo.
    echo ✅ تم تثبيت جميع المتطلبات بنجاح!
    echo 🚀 يمكنك الآن تشغيل التطبيق باستخدام: run_depot_downloader.bat
) else (
    echo.
    echo ❌ فشل في تثبيت بعض المتطلبات
    echo يرجى التحقق من اتصال الإنترنت وإعادة المحاولة
)

echo.
pause
