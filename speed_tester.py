"""
اختبار سرعة الشبكة
"""
import speedtest
import threading
import time
import requests
import socket

class SpeedTester:
    def __init__(self):
        self.testing = False
        self.last_results = {}
        self.test_history = []
        
    def test_internet_speed(self, callback=None):
        """اختبار سرعة الإنترنت باستخدام speedtest"""
        self.testing = True
        
        try:
            if callback:
                callback("جاري البحث عن أفضل خادم...")
            
            # إنشاء كائن speedtest
            st = speedtest.Speedtest()
            
            if callback:
                callback("جاري اختيار أفضل خادم...")
            
            # اختيار أفضل خادم
            st.get_best_server()
            
            if callback:
                callback("جاري اختبار سرعة التحميل...")
            
            # اختبار سرعة التحميل
            download_speed = st.download()
            
            if callback:
                callback("جاري اختبار سرعة الرفع...")
            
            # اختبار سرعة الرفع
            upload_speed = st.upload()
            
            if callback:
                callback("جاري اختبار زمن الاستجابة...")
            
            # اختبار ping
            ping = st.results.ping
            
            # تحويل من bits إلى bytes ثم إلى Mbps
            download_mbps = download_speed / 1_000_000
            upload_mbps = upload_speed / 1_000_000
            
            results = {
                'download_speed': download_mbps,
                'upload_speed': upload_mbps,
                'ping': ping,
                'server': st.results.server,
                'timestamp': time.time(),
                'test_type': 'speedtest'
            }
            
            self.last_results = results
            self.test_history.append(results)
            
            if callback:
                callback("تم الانتهاء من الاختبار")
            
            return results
            
        except Exception as e:
            error_result = {
                'error': str(e),
                'timestamp': time.time(),
                'test_type': 'speedtest'
            }
            
            if callback:
                callback(f"خطأ في الاختبار: {e}")
            
            return error_result
        
        finally:
            self.testing = False
    
    def test_local_speed(self, target_ip, callback=None):
        """اختبار سرعة الشبكة المحلية"""
        try:
            if callback:
                callback(f"جاري اختبار الاتصال مع {target_ip}...")
            
            # اختبار ping
            ping_time = self.ping_test(target_ip)
            
            if callback:
                callback("جاري اختبار سرعة النقل...")
            
            # اختبار سرعة النقل (محاكاة)
            transfer_speed = self.transfer_test(target_ip)
            
            results = {
                'target_ip': target_ip,
                'ping': ping_time,
                'transfer_speed': transfer_speed,
                'timestamp': time.time(),
                'test_type': 'local'
            }
            
            if callback:
                callback("تم الانتهاء من اختبار الشبكة المحلية")
            
            return results
            
        except Exception as e:
            error_result = {
                'error': str(e),
                'target_ip': target_ip,
                'timestamp': time.time(),
                'test_type': 'local'
            }
            
            if callback:
                callback(f"خطأ في اختبار الشبكة المحلية: {e}")
            
            return error_result
    
    def ping_test(self, host, count=4):
        """اختبار ping"""
        import subprocess
        
        try:
            # تشغيل ping
            result = subprocess.run(
                ['ping', '-n', str(count), host],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                # استخراج متوسط وقت الاستجابة
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'Average' in line or 'متوسط' in line:
                        # استخراج الرقم من النص
                        import re
                        numbers = re.findall(r'\d+', line)
                        if numbers:
                            return float(numbers[-1])
                
                # إذا لم نجد متوسط، نحسبه من النتائج
                times = []
                for line in lines:
                    if 'time=' in line or 'الوقت=' in line:
                        import re
                        match = re.search(r'time[=<]\s*(\d+)', line)
                        if match:
                            times.append(float(match.group(1)))
                
                if times:
                    return sum(times) / len(times)
            
            return -1  # فشل في الاتصال
            
        except Exception as e:
            print(f"خطأ في ping: {e}")
            return -1
    
    def transfer_test(self, host, port=80, data_size=1024):
        """اختبار سرعة النقل (محاكاة بسيطة)"""
        try:
            start_time = time.time()
            
            # محاولة الاتصال بالمضيف
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            
            result = sock.connect_ex((host, port))
            
            if result == 0:
                # إرسال بيانات تجريبية
                test_data = b'0' * data_size
                sock.send(test_data)
                
                end_time = time.time()
                duration = end_time - start_time
                
                # حساب السرعة (KB/s)
                speed = (data_size / 1024) / duration if duration > 0 else 0
                
                sock.close()
                return speed
            else:
                sock.close()
                return 0
                
        except Exception as e:
            print(f"خطأ في اختبار النقل: {e}")
            return 0
    
    def test_dns_speed(self, dns_servers=None):
        """اختبار سرعة خوادم DNS"""
        if dns_servers is None:
            dns_servers = [
                '*******',      # Google
                '*******',      # Cloudflare
                '**************', # OpenDNS
                '9.9.9.9'       # Quad9
            ]
        
        results = {}
        
        for dns in dns_servers:
            try:
                start_time = time.time()
                
                # محاولة الاتصال بخادم DNS
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(3)
                result = sock.connect_ex((dns, 53))
                
                if result == 0:
                    end_time = time.time()
                    response_time = (end_time - start_time) * 1000  # بالميلي ثانية
                    results[dns] = response_time
                else:
                    results[dns] = -1
                
                sock.close()
                
            except Exception as e:
                results[dns] = -1
        
        return results
    
    def test_website_speed(self, urls=None):
        """اختبار سرعة الوصول للمواقع"""
        if urls is None:
            urls = [
                'https://www.google.com',
                'https://www.facebook.com',
                'https://www.youtube.com',
                'https://www.github.com'
            ]
        
        results = {}
        
        for url in urls:
            try:
                start_time = time.time()
                response = requests.get(url, timeout=10)
                end_time = time.time()
                
                response_time = (end_time - start_time) * 1000  # بالميلي ثانية
                
                results[url] = {
                    'response_time': response_time,
                    'status_code': response.status_code,
                    'success': response.status_code == 200
                }
                
            except Exception as e:
                results[url] = {
                    'response_time': -1,
                    'status_code': -1,
                    'success': False,
                    'error': str(e)
                }
        
        return results
    
    def start_speed_test(self, test_type='internet', target=None, callback=None):
        """بدء اختبار السرعة في خيط منفصل"""
        def run_test():
            if test_type == 'internet':
                return self.test_internet_speed(callback)
            elif test_type == 'local' and target:
                return self.test_local_speed(target, callback)
            elif test_type == 'dns':
                results = self.test_dns_speed()
                if callback:
                    callback("تم الانتهاء من اختبار DNS")
                return results
            elif test_type == 'websites':
                results = self.test_website_speed()
                if callback:
                    callback("تم الانتهاء من اختبار المواقع")
                return results
        
        if not self.testing:
            test_thread = threading.Thread(target=run_test)
            test_thread.daemon = True
            test_thread.start()
            return test_thread
        else:
            if callback:
                callback("اختبار آخر قيد التشغيل...")
            return None
    
    def get_test_history(self, limit=10):
        """الحصول على تاريخ الاختبارات"""
        return self.test_history[-limit:] if self.test_history else []
    
    def format_speed(self, speed_mbps):
        """تنسيق السرعة"""
        if speed_mbps >= 1000:
            return f"{speed_mbps/1000:.2f} Gbps"
        else:
            return f"{speed_mbps:.2f} Mbps"
    
    def get_speed_rating(self, download_speed):
        """تقييم جودة السرعة"""
        if download_speed >= 100:
            return "ممتازة"
        elif download_speed >= 50:
            return "جيدة جداً"
        elif download_speed >= 25:
            return "جيدة"
        elif download_speed >= 10:
            return "متوسطة"
        elif download_speed >= 5:
            return "ضعيفة"
        else:
            return "ضعيفة جداً"
