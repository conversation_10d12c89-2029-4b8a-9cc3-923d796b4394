{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"DepotDownloaderMod/2.7.4": {"dependencies": {"Microsoft.Windows.CsWin32": "0.3.106", "QRCoder": "1.6.0", "SteamKit2": "3.0.0", "protobuf-net": "3.2.45"}, "runtime": {"DepotDownloaderMod.dll": {}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Windows.CsWin32/0.3.106": {"dependencies": {"Microsoft.Windows.SDK.Win32Docs": "0.1.42-alpha", "Microsoft.Windows.SDK.Win32Metadata": "60.0.34-preview", "Microsoft.Windows.WDK.Win32Metadata": "0.11.4-experimental"}}, "Microsoft.Windows.SDK.Win32Docs/0.1.42-alpha": {}, "Microsoft.Windows.SDK.Win32Metadata/60.0.34-preview": {}, "Microsoft.Windows.WDK.Win32Metadata/0.11.4-experimental": {"dependencies": {"Microsoft.Windows.SDK.Win32Metadata": "60.0.34-preview"}}, "protobuf-net/3.2.45": {"dependencies": {"protobuf-net.Core": "3.2.45"}, "runtime": {"lib/net6.0/protobuf-net.dll": {"assemblyVersion": "*******", "fileVersion": "3.2.45.36865"}}}, "protobuf-net.Core/3.2.45": {"dependencies": {"System.Collections.Immutable": "7.0.0"}, "runtime": {"lib/net6.0/protobuf-net.Core.dll": {"assemblyVersion": "*******", "fileVersion": "3.2.45.36865"}}}, "QRCoder/1.6.0": {"runtime": {"lib/net6.0/QRCoder.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SteamKit2/3.0.0": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.IO.Hashing": "8.0.0", "protobuf-net": "3.2.45"}, "runtime": {"lib/net8.0/SteamKit2.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Collections.Immutable/7.0.0": {}, "System.IO.Hashing/8.0.0": {"runtime": {"lib/net8.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Principal.Windows/5.0.0": {}}}, "libraries": {"DepotDownloaderMod/2.7.4": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Windows.CsWin32/0.3.106": {"type": "package", "serviceable": true, "sha512": "sha512-Mx5fK7uN6fwLR4wUghs6//HonAnwPBNmC2oonyJVhCUlHS/r6SUS3NkBc3+gaQiv+0/9bqdj1oSCKQFkNI+21Q==", "path": "microsoft.windows.cswin32/0.3.106", "hashPath": "microsoft.windows.cswin32.0.3.106.nupkg.sha512"}, "Microsoft.Windows.SDK.Win32Docs/0.1.42-alpha": {"type": "package", "serviceable": true, "sha512": "sha512-Z/9po23gUA9aoukirh2ItMU2ZS9++Js9Gdds9fu5yuMojDrmArvY2y+tq9985tR3cxFxpZO1O35Wjfo0khj5HA==", "path": "microsoft.windows.sdk.win32docs/0.1.42-alpha", "hashPath": "microsoft.windows.sdk.win32docs.0.1.42-alpha.nupkg.sha512"}, "Microsoft.Windows.SDK.Win32Metadata/60.0.34-preview": {"type": "package", "serviceable": true, "sha512": "sha512-TA3DUNi4CTeo+ItTXBnGZFt2159XOGSl0UOlG5vjDj4WHqZjhwYyyUnzOtrbCERiSaP2Hzg7otJNWwOSZgutyA==", "path": "microsoft.windows.sdk.win32metadata/60.0.34-preview", "hashPath": "microsoft.windows.sdk.win32metadata.60.0.34-preview.nupkg.sha512"}, "Microsoft.Windows.WDK.Win32Metadata/0.11.4-experimental": {"type": "package", "serviceable": true, "sha512": "sha512-bf5MCmUyZf0gBlYQjx9UpRAZWBkRndyt9XicR+UNLvAUAFTZQbu6YaX/sNKZlR98Grn0gydfh/yT4I3vc0AIQA==", "path": "microsoft.windows.wdk.win32metadata/0.11.4-experimental", "hashPath": "microsoft.windows.wdk.win32metadata.0.11.4-experimental.nupkg.sha512"}, "protobuf-net/3.2.45": {"type": "package", "serviceable": true, "sha512": "sha512-5UZ/ukUHcGbFSl7vNMrHsfjqdxusdd9w7w0fCEXzf3UUtsrGNVCzV5SmF+sCHAbnRV2qPcD1ixiDP7Aj8lX/HA==", "path": "protobuf-net/3.2.45", "hashPath": "protobuf-net.3.2.45.nupkg.sha512"}, "protobuf-net.Core/3.2.45": {"type": "package", "serviceable": true, "sha512": "sha512-PMWatW2NrT1uTXD7etJ4VdQ0wWZLFrIfdRGppD2QX7nzZ0+kIzqhq551u6ZiXJHWJgG4hWFEkSnUnt2aB6posg==", "path": "protobuf-net.core/3.2.45", "hashPath": "protobuf-net.core.3.2.45.nupkg.sha512"}, "QRCoder/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-XmPA81eo+oRxBuyVdswsSkTGTE1d3thfF11Z1PdD7oB56A6HU4G4AAOdySmGRMb/cljwlFTMWUtosGEnwpS6GA==", "path": "qrcoder/1.6.0", "hashPath": "qrcoder.1.6.0.nupkg.sha512"}, "SteamKit2/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1d45S7gJPqKKlD6FEirZGjU72xFKnVYVNwM2/E5PQ1LxNcymfWUFlaqRH7hfDRBatFvaxbI6Cqc4x8eFYqgn/w==", "path": "steamkit2/3.0.0", "hashPath": "steamkit2.3.0.0.nupkg.sha512"}, "System.Collections.Immutable/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dQPcs0U1IKnBdRDBkrCTi1FoajSTBzLcVTpjO4MBCMC7f4pDOIPzgBoX8JjG7X6uZRJ8EBxsi8+DR1JuwjnzOQ==", "path": "system.collections.immutable/7.0.0", "hashPath": "system.collections.immutable.7.0.0.nupkg.sha512"}, "System.IO.Hashing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ne1843evDugl0md7Fjzy6QjJrzsjh46ZKbhf8GwBXb5f/gw97J4bxMs0NQKifDuThh/f0bZ0e62NPl1jzTuRqA==", "path": "system.io.hashing/8.0.0", "hashPath": "system.io.hashing.8.0.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}}}