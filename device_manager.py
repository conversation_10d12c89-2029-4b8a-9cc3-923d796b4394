"""
إدارة وحظر الأجهزة على الشبكة
"""
import subprocess
import json
import os
import time
from collections import defaultdict

class DeviceManager:
    def __init__(self):
        self.blocked_devices = set()
        self.device_rules = {}
        self.bandwidth_limits = {}
        self.blocked_file = "blocked_devices.json"
        self.rules_file = "device_rules.json"
        
        # تحميل البيانات المحفوظة
        self.load_blocked_devices()
        self.load_device_rules()
    
    def block_device_by_ip(self, ip, method='firewall'):
        """حظر جهاز بواسطة IP"""
        try:
            if method == 'firewall':
                return self._block_with_firewall(ip)
            elif method == 'arp':
                return self._block_with_arp(ip)
            else:
                return False
        except Exception as e:
            print(f"خطأ في حظر الجهاز {ip}: {e}")
            return False
    
    def _block_with_firewall(self, ip):
        """حظر باستخدام Windows Firewall"""
        try:
            # إنشاء قاعدة حظر في Windows Firewall
            rule_name = f"Block_Device_{ip.replace('.', '_')}"
            
            # حظر الاتصالات الواردة
            cmd_in = [
                'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                f'name={rule_name}_IN',
                'dir=in',
                'action=block',
                f'remoteip={ip}'
            ]
            
            # حظر الاتصالات الصادرة
            cmd_out = [
                'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                f'name={rule_name}_OUT',
                'dir=out',
                'action=block',
                f'remoteip={ip}'
            ]
            
            # تشغيل الأوامر
            result_in = subprocess.run(cmd_in, capture_output=True, text=True)
            result_out = subprocess.run(cmd_out, capture_output=True, text=True)
            
            if result_in.returncode == 0 and result_out.returncode == 0:
                self.blocked_devices.add(ip)
                self.save_blocked_devices()
                print(f"تم حظر الجهاز {ip} بنجاح")
                return True
            else:
                print(f"فشل في حظر الجهاز {ip}")
                print(f"خطأ الدخول: {result_in.stderr}")
                print(f"خطأ الخروج: {result_out.stderr}")
                return False
                
        except Exception as e:
            print(f"خطأ في حظر الجهاز بـ Firewall: {e}")
            return False
    
    def _block_with_arp(self, ip):
        """حظر باستخدام ARP spoofing (متقدم)"""
        try:
            # هذه الطريقة تتطلب صلاحيات إدارية وأدوات إضافية
            # سنستخدم طريقة بديلة بإضافة ARP entry خاطئ
            
            # إضافة ARP entry خاطئ
            cmd = ['arp', '-s', ip, '00-00-00-00-00-00']
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.blocked_devices.add(ip)
                self.save_blocked_devices()
                print(f"تم حظر الجهاز {ip} باستخدام ARP")
                return True
            else:
                print(f"فشل في حظر الجهاز {ip} باستخدام ARP")
                return False
                
        except Exception as e:
            print(f"خطأ في حظر الجهاز بـ ARP: {e}")
            return False
    
    def unblock_device(self, ip):
        """إلغاء حظر جهاز"""
        try:
            # إزالة قواعد Firewall
            rule_name = f"Block_Device_{ip.replace('.', '_')}"
            
            cmd_in = [
                'netsh', 'advfirewall', 'firewall', 'delete', 'rule',
                f'name={rule_name}_IN'
            ]
            
            cmd_out = [
                'netsh', 'advfirewall', 'firewall', 'delete', 'rule',
                f'name={rule_name}_OUT'
            ]
            
            subprocess.run(cmd_in, capture_output=True)
            subprocess.run(cmd_out, capture_output=True)
            
            # إزالة ARP entry
            cmd_arp = ['arp', '-d', ip]
            subprocess.run(cmd_arp, capture_output=True)
            
            # إزالة من القائمة
            self.blocked_devices.discard(ip)
            self.save_blocked_devices()
            
            print(f"تم إلغاء حظر الجهاز {ip}")
            return True
            
        except Exception as e:
            print(f"خطأ في إلغاء حظر الجهاز {ip}: {e}")
            return False
    
    def is_device_blocked(self, ip):
        """التحقق من حظر الجهاز"""
        return ip in self.blocked_devices
    
    def get_blocked_devices(self):
        """الحصول على قائمة الأجهزة المحظورة"""
        return list(self.blocked_devices)
    
    def set_bandwidth_limit(self, ip, download_limit=None, upload_limit=None):
        """تحديد حد استهلاك البيانات للجهاز"""
        try:
            # هذه الميزة تتطلب أدوات متقدمة مثل Traffic Control
            # سنحفظ الحدود في ملف للمراقبة
            
            self.bandwidth_limits[ip] = {
                'download_limit': download_limit,  # KB/s
                'upload_limit': upload_limit,      # KB/s
                'timestamp': time.time()
            }
            
            self.save_device_rules()
            print(f"تم تحديد حدود البيانات للجهاز {ip}")
            return True
            
        except Exception as e:
            print(f"خطأ في تحديد حدود البيانات: {e}")
            return False
    
    def remove_bandwidth_limit(self, ip):
        """إزالة حدود البيانات"""
        if ip in self.bandwidth_limits:
            del self.bandwidth_limits[ip]
            self.save_device_rules()
            return True
        return False
    
    def get_device_bandwidth_limit(self, ip):
        """الحصول على حدود البيانات للجهاز"""
        return self.bandwidth_limits.get(ip, None)
    
    def set_device_rule(self, ip, rule_type, rule_value):
        """تحديد قاعدة للجهاز"""
        if ip not in self.device_rules:
            self.device_rules[ip] = {}
        
        self.device_rules[ip][rule_type] = {
            'value': rule_value,
            'timestamp': time.time()
        }
        
        self.save_device_rules()
    
    def get_device_rules(self, ip):
        """الحصول على قواعد الجهاز"""
        return self.device_rules.get(ip, {})
    
    def block_multiple_devices(self, ip_list, method='firewall'):
        """حظر عدة أجهزة"""
        results = {}
        for ip in ip_list:
            results[ip] = self.block_device_by_ip(ip, method)
        return results
    
    def unblock_multiple_devices(self, ip_list):
        """إلغاء حظر عدة أجهزة"""
        results = {}
        for ip in ip_list:
            results[ip] = self.unblock_device(ip)
        return results
    
    def schedule_block(self, ip, duration_minutes):
        """جدولة حظر مؤقت"""
        import threading
        
        def temporary_block():
            if self.block_device_by_ip(ip):
                print(f"تم حظر {ip} لمدة {duration_minutes} دقيقة")
                time.sleep(duration_minutes * 60)
                self.unblock_device(ip)
                print(f"تم إلغاء حظر {ip} تلقائياً")
        
        block_thread = threading.Thread(target=temporary_block)
        block_thread.daemon = True
        block_thread.start()
    
    def get_firewall_rules(self):
        """الحصول على قواعد Firewall الحالية"""
        try:
            cmd = ['netsh', 'advfirewall', 'firewall', 'show', 'rule', 'name=all']
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # تحليل النتائج للعثور على قواعد الحظر
                rules = []
                lines = result.stdout.split('\n')
                current_rule = {}
                
                for line in lines:
                    line = line.strip()
                    if line.startswith('Rule Name:'):
                        if current_rule and 'Block_Device_' in current_rule.get('name', ''):
                            rules.append(current_rule)
                        current_rule = {'name': line.split(':', 1)[1].strip()}
                    elif line.startswith('Direction:'):
                        current_rule['direction'] = line.split(':', 1)[1].strip()
                    elif line.startswith('Action:'):
                        current_rule['action'] = line.split(':', 1)[1].strip()
                    elif line.startswith('Remote IP:'):
                        current_rule['remote_ip'] = line.split(':', 1)[1].strip()
                
                return rules
            else:
                return []
                
        except Exception as e:
            print(f"خطأ في الحصول على قواعد Firewall: {e}")
            return []
    
    def cleanup_firewall_rules(self):
        """تنظيف قواعد Firewall القديمة"""
        try:
            rules = self.get_firewall_rules()
            cleaned = 0
            
            for rule in rules:
                if 'Block_Device_' in rule.get('name', ''):
                    cmd = ['netsh', 'advfirewall', 'firewall', 'delete', 'rule', f"name={rule['name']}"]
                    result = subprocess.run(cmd, capture_output=True)
                    if result.returncode == 0:
                        cleaned += 1
            
            print(f"تم تنظيف {cleaned} قاعدة من Firewall")
            return cleaned
            
        except Exception as e:
            print(f"خطأ في تنظيف قواعد Firewall: {e}")
            return 0
    
    def save_blocked_devices(self):
        """حفظ قائمة الأجهزة المحظورة"""
        try:
            with open(self.blocked_file, 'w', encoding='utf-8') as f:
                json.dump(list(self.blocked_devices), f, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الأجهزة المحظورة: {e}")
    
    def load_blocked_devices(self):
        """تحميل قائمة الأجهزة المحظورة"""
        try:
            if os.path.exists(self.blocked_file):
                with open(self.blocked_file, 'r', encoding='utf-8') as f:
                    blocked_list = json.load(f)
                    self.blocked_devices = set(blocked_list)
        except Exception as e:
            print(f"خطأ في تحميل الأجهزة المحظورة: {e}")
    
    def save_device_rules(self):
        """حفظ قواعد الأجهزة"""
        try:
            save_data = {
                'bandwidth_limits': self.bandwidth_limits,
                'device_rules': self.device_rules
            }
            with open(self.rules_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ قواعد الأجهزة: {e}")
    
    def load_device_rules(self):
        """تحميل قواعد الأجهزة"""
        try:
            if os.path.exists(self.rules_file):
                with open(self.rules_file, 'r', encoding='utf-8') as f:
                    save_data = json.load(f)
                    self.bandwidth_limits = save_data.get('bandwidth_limits', {})
                    self.device_rules = save_data.get('device_rules', {})
        except Exception as e:
            print(f"خطأ في تحميل قواعد الأجهزة: {e}")
    
    def get_device_status(self, ip):
        """الحصول على حالة الجهاز الشاملة"""
        return {
            'ip': ip,
            'is_blocked': self.is_device_blocked(ip),
            'bandwidth_limit': self.get_device_bandwidth_limit(ip),
            'rules': self.get_device_rules(ip),
            'last_updated': time.time()
        }
