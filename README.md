# Steam Lua Injector

برنامج لحقن وتعديل ملفات Lua في ألعاب Steam للاستخدام الشخصي.

## المميزات

- البحث عن الألعاب باستخدام Steam App ID
- عرض جميع ملفات Lua في العبة
- حقن كود Lua مخصص في الملفات الموجودة
- استبدال ملفات Lua بملفات جديدة
- إنشاء نسخ احتياطية تلقائية
- استعادة النسخ الاحتياطية
- واجهة مستخدم رسومية سهلة الاستخدام

## المتطلبات

- Python 3.7 أو أحدث
- Windows (للوصول إلى Steam)
- Steam مثبت على النظام

## التثبيت

1. تأكد من تثبيت Python
2. قم بتثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

## الاستخدام

1. تشغيل البرنامج:
```bash
python main.py
```

2. إدخال App ID للعبة المطلوبة
3. الضغط على "تحميل العبة"
4. تحديد ملف Lua من القائمة
5. اختيار العملية المطلوبة (حقن كود، استبدال ملف، إلخ)

## كيفية العثور على App ID

يمكن العثور على App ID للعبة من:
- صفحة العبة في Steam Store (في الرابط)
- SteamDB.info
- ملفات Steam في مجلد steamapps

## الوظائف الرئيسية

### حقن الكود
- إضافة كود Lua مخصص في بداية أو نهاية الملف
- إنشاء نسخة احتياطية تلقائية قبل التعديل

### استبدال الملف
- استبدال ملف Lua كامل بملف جديد
- الاحتفاظ بنسخة احتياطية من الملف الأصلي

### النسخ الاحتياطية
- إنشاء نسخ احتياطية يدوية
- استعادة النسخ الاحتياطية عند الحاجة

## تحذيرات مهمة

⚠️ **للاستخدام الشخصي فقط**
⚠️ **قم بإنشاء نسخ احتياطية قبل أي تعديل**
⚠️ **تأكد من إغلاق العبة قبل التعديل**
⚠️ **قد يؤثر على حفظ الألعاب أو الإنجازات**

## هيكل المشروع

```
steam-lua-injector/
├── main.py              # الملف الرئيسي
├── gui.py               # واجهة المستخدم
├── steam_injector.py    # منطق Steam
├── lua_handler.py       # معالج ملفات Lua
├── config.py            # إعدادات البرنامج
├── requirements.txt     # المتطلبات
└── README.md           # هذا الملف
```

## الدعم

هذا البرنامج مطور للاستخدام الشخصي. استخدمه على مسؤوليتك الخاصة.

## الترخيص

للاستخدام الشخصي فقط.
