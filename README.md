# 🌐 مراقب الشبكة البسيط مع حظر الأجهزة

برنامج شامل لمراقبة الشبكة المحلية **مع إمكانية حظر الأجهزة** وواجهة رسومية سهلة الاستخدام.

## ✨ الميزات الرئيسية

### 🔍 فحص الأجهزة المتصلة
- **اكتشاف تلقائي** لجميع الأجهزة على الشبكة
- **عرض معلومات مفصلة**: IP، MAC Address، اسم الجهاز، الشركة المصنعة
- **فحص سريع ومتوازي** للشبكة الكاملة
- **تحديث مستمر** لحالة الأجهزة

### 📊 مراقبة استهلاك البيانات
- **مراقبة في الوقت الفعلي** لاستهلاك البيانات
- **تتبع السرعات** للتحميل والرفع
- **إحصائيات مفصلة** لكل واجهة شبكة
- **حفظ تاريخ الاستخدام** وإمكانية استعادته

### ⚡ اختبار سرعة الشبكة
- **اختبار سرعة الإنترنت** باستخدام Speedtest
- **اختبار سرعة DNS** لخوادم مختلفة
- **اختبار سرعة المواقع** الشائعة
- **تقييم جودة الاتصال** وعرض التوصيات

### 🛡️ إدارة وحظر الأجهزة ⭐ جديد!
- **حظر حقيقي** للأجهزة من الوصول للإنترنت
- **استخدام Windows Firewall** لحظر موثوق وآمن
- **إدارة شاملة** للأجهزة المحظورة مع التفاصيل
- **حظر سريع** من نافذة تفاصيل الجهاز
- **حفظ تلقائي** لقائمة الأجهزة المحظورة

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية
- **Windows 10/11** (مطلوب لوظائف الحظر)
- **Python 3.7+** مثبت على النظام
- **صلاحيات المدير** (لوظائف الحظر)

### التثبيت السريع

1. **تحميل المشروع**
```bash
git clone [repository-url]
cd network-monitor
```

2. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

3. **التشغيل**

**⚠️ للحصول على ميزات الحظر (مستحسن):**
```bash
run_as_admin.bat
```

**أو للمراقبة فقط:**
```bash
python simple_network_monitor.py
```

أو استخدم الملف المساعد:
```bash
run_network_monitor.bat
```

## 🛡️ ميزات الحظر الجديدة ⭐

### **🔒 حظر الأجهزة:**
1. **اذهب لتبويب "إدارة الأجهزة"**
2. **أدخل IP الجهاز** المراد حظره
3. **اضغط "حظر"** وأكد العملية
4. **✅ الجهاز محظور من الإنترنت!**

### **📋 إدارة الأجهزة المحظورة:**
- **عرض قائمة كاملة** بالأجهزة المحظورة
- **تفاصيل الحظر**: IP، اسم الجهاز، وقت الحظر
- **إلغاء حظر فردي** أو **جماعي**
- **حفظ تلقائي** لقائمة الحظر

### **⚡ حظر سريع:**
- **انقر مرتين** على أي جهاز في قائمة الأجهزة
- **اضغط "حظر الجهاز"** من نافذة التفاصيل
- **حظر فوري** بنقرة واحدة!

### **🧪 اختبار الحظر:**
```bash
test_blocking.bat
```
أو للاختبار مع جهاز محدد:
```bash
python test_blocking.py *************
```

## 📱 دليل الاستخدام

### تبويب "الأجهزة المتصلة"

#### فحص الشبكة
1. اضغط **"فحص الشبكة"** لبدء البحث عن الأجهزة
2. انتظر حتى اكتمال الفحص (قد يستغرق 1-2 دقيقة)
3. ستظهر جميع الأجهزة المكتشفة في الجدول

#### معلومات الأجهزة
- **IP Address**: عنوان IP للجهاز
- **MAC Address**: العنوان الفيزيائي للجهاز
- **اسم الجهاز**: اسم الجهاز على الشبكة
- **الشركة المصنعة**: نوع الجهاز (إن أمكن تحديده)
- **الحالة**: متصل أو محظور

### تبويب "مراقبة البيانات"

#### بدء المراقبة
1. اضغط **"بدء المراقبة"** لتتبع استهلاك البيانات
2. ستظهر الإحصائيات في الوقت الفعلي
3. يمكن إيقاف المراقبة في أي وقت

#### قراءة الإحصائيات
- **إجمالي التحميل/الرفع**: البيانات المنقولة منذ بدء الجلسة
- **السرعة الحالية**: سرعة النقل الحالية
- **أقصى سرعة**: أعلى سرعة تم تسجيلها

### تبويب "اختبار السرعة"

#### اختبار سرعة الإنترنت
1. اضغط **"اختبار سرعة الإنترنت"**
2. انتظر حتى اكتمال الاختبار (2-3 دقائق)
3. ستظهر النتائج مع تقييم جودة الاتصال

#### اختبارات أخرى
- **اختبار DNS**: قياس سرعة خوادم DNS المختلفة
- **اختبار المواقع**: قياس سرعة الوصول للمواقع الشائعة

### تبويب "إدارة الأجهزة"

#### حظر جهاز
1. أدخل **IP Address** للجهاز المراد حظره
2. اضغط **"حظر"**
3. سيتم منع الجهاز من الوصول للإنترنت

#### إلغاء الحظر
1. حدد الجهاز من قائمة الأجهزة المحظورة
2. اضغط **"إلغاء حظر المحدد"**
3. أو استخدم **"إلغاء حظر الكل"** لإلغاء جميع الحظر

## ⚙️ الإعدادات المتقدمة

### ملفات الإعدادات والأدوات ⭐
- `simple_network_monitor.py`: البرنامج الرئيسي مع الحظر
- `run_as_admin.bat`: تشغيل بصلاحيات المدير ⭐
- `test_blocking.py`: أداة اختبار الحظر ⭐
- `test_blocking.bat`: تشغيل أداة الاختبار ⭐
- `blocked_devices.json`: قائمة الأجهزة المحظورة ⭐
- `BLOCKING_GUIDE.md`: دليل شامل لميزات الحظر ⭐
- `TROUBLESHOOTING.md`: استكشاف أخطاء الحظر ⭐
- `device_rules.json`: قواعد إدارة الأجهزة
- `network_usage.json`: تاريخ استهلاك البيانات

### تخصيص الإعدادات
يمكن تعديل الإعدادات في بداية كل ملف Python:
- `network_scanner.py`: إعدادات فحص الشبكة
- `bandwidth_monitor.py`: إعدادات مراقبة البيانات
- `speed_tester.py`: إعدادات اختبار السرعة

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

#### "فشل في فحص الشبكة"
- تأكد من اتصالك بالشبكة
- تحقق من إعدادات Firewall
- جرب تشغيل البرنامج كمدير

#### "فشل في حظر الجهاز" أو "مش بيتحظر" ⭐
**الحل السريع:**
1. **شغل `test_blocking.bat`** للتشخيص
2. **تأكد من تشغيل البرنامج كمدير** (`run_as_admin.bat`)
3. **تحقق من تفعيل Windows Firewall**
4. **اقرأ `TROUBLESHOOTING.md`** للحلول التفصيلية

**خطوات إضافية:**
- تحقق من صحة IP Address
- جرب إعادة تشغيل الجهاز المحظور
- تأكد من عدم استخدام VPN على الجهاز المحظور

#### "خطأ في اختبار السرعة"
- تحقق من اتصال الإنترنت
- جرب إغلاق البرامج الأخرى المستهلكة للإنترنت
- تأكد من عدم وجود VPN نشط

### الحصول على المساعدة
1. تحقق من رسائل الخطأ في شريط الحالة
2. راجع ملف `requirements.txt` للتأكد من تثبيت جميع المكتبات
3. جرب إعادة تشغيل البرنامج بصلاحيات المدير

## ⚠️ تحذيرات مهمة

### الاستخدام القانوني
- **استخدم البرنامج على شبكتك الخاصة فقط**
- **لا تستخدمه على شبكات لا تملكها**
- **احترم خصوصية الآخرين**

### الأمان
- **احتفظ بنسخة احتياطية** من إعدادات الشبكة
- **لا تحظر جهازك الخاص** لتجنب فقدان الاتصال
- **استخدم وظائف الحظر بحذر**

## 📋 المتطلبات التقنية

### المكتبات المطلوبة
```
psutil>=5.8.0
netifaces>=0.11.0
speedtest-cli>=2.1.3
netaddr>=0.8.0
scapy>=2.4.5
requests>=2.25.1
```

### النظام
- **نظام التشغيل**: Windows 10/11
- **الذاكرة**: 512 MB RAM كحد أدنى
- **المساحة**: 100 MB مساحة فارغة
- **الشبكة**: اتصال بالإنترنت (لاختبار السرعة)

## 🔄 التحديثات والميزات

### ميزات مكتملة ✅
- [x] ✅ **حظر الأجهزة الحقيقي** ⭐
- [x] ✅ **إدارة شاملة للأجهزة المحظورة** ⭐
- [x] ✅ **أدوات التشخيص والاختبار** ⭐
- [x] ✅ **حظر سريع من نافذة التفاصيل** ⭐
- [x] ✅ **حفظ تلقائي لقائمة الحظر** ⭐

### ميزات مخططة
- [ ] دعم أنظمة Linux و macOS
- [ ] رسوم بيانية لاستهلاك البيانات
- [ ] تنبيهات عند اكتشاف أجهزة جديدة
- [ ] تصدير التقارير إلى PDF
- [ ] واجهة ويب للإدارة عن بُعد

## 📄 الترخيص

هذا البرنامج مطور للاستخدام الشخصي وإدارة الشبكات المحلية.
استخدمه بمسؤولية واحترم القوانين المحلية.

---

**🎉 استمتع بمراقبة شبكتك والتحكم الكامل في الأجهزة!**

**⭐ الآن مع ميزات الحظر الحقيقية!**

### 🚀 **ابدأ الآن:**
```bash
run_as_admin.bat
```

### 🧪 **اختبر الحظر:**
```bash
test_blocking.bat
```

### 📖 **للمساعدة:**
- `BLOCKING_GUIDE.md` - دليل شامل للحظر
- `TROUBLESHOOTING.md` - حل المشاكل
