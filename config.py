"""
إعدادات برنامج Steam Lua Injector
"""
import os
import winreg
from pathlib import Path

class Config:
    def __init__(self):
        self.steam_path = self.get_steam_path()
        self.steamapps_path = self.get_steamapps_path()
        
    def get_steam_path(self):
        """البحث عن مسار Steam في الريجستري"""
        try:
            # البحث في الريجستري عن مسار Steam
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                               r"SOFTWARE\WOW6432Node\Valve\Steam")
            steam_path, _ = winreg.QueryValueEx(key, "InstallPath")
            winreg.CloseKey(key)
            return steam_path
        except:
            # مسارات افتراضية إذا لم يتم العثور على Steam في الريجستري
            default_paths = [
                r"C:\Program Files (x86)\Steam",
                r"C:\Program Files\Steam",
                r"D:\Steam",
                r"E:\Steam"
            ]
            for path in default_paths:
                if os.path.exists(path):
                    return path
            return None
    
    def get_steamapps_path(self):
        """الحصول على مسار مجلد steamapps"""
        if self.steam_path:
            steamapps = os.path.join(self.steam_path, "steamapps")
            if os.path.exists(steamapps):
                return steamapps
        return None
    
    def get_game_path(self, app_id):
        """الحصول على مسار العبة بواسطة App ID"""
        if not self.steamapps_path:
            return None
            
        # البحث في ملف appmanifest
        manifest_file = os.path.join(self.steamapps_path, f"appmanifest_{app_id}.acf")
        if not os.path.exists(manifest_file):
            return None
            
        try:
            with open(manifest_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # استخراج اسم المجلد من الملف
            for line in content.split('\n'):
                if '"installdir"' in line:
                    folder_name = line.split('"')[3]
                    game_path = os.path.join(self.steamapps_path, "common", folder_name)
                    if os.path.exists(game_path):
                        return game_path
        except:
            pass
            
        return None
