# 🎮 Steam Depot Downloader Pro

## 🌟 **نسخة محسنة من DepotDownloader مع واجهة رسومية متقدمة**

تطبيق شامل لتحميل محتوى Steam Depots مع واجهة مستخدم حديثة وميزات متقدمة.

---

## ✨ **الميزات الرئيسية**

### 🎯 **واجهة رسومية متقدمة:**
- **تصميم عصري** مع ألوان احترافية
- **تبويبات منظمة** للوظائف المختلفة
- **شريط تقدم** لمتابعة التحميل
- **سجل مفصل** لجميع العمليات

### 🔍 **البحث والاكتشاف:**
- **البحث عن App ID** من Steam Store
- **عرض قائمة Depots** للتطبيق
- **اختيار سريع** من القوائم
- **معاينة المحتوى** قبل التحميل

### ⚙️ **إعدادات متقدمة:**
- **عدد الاتصالات المتزامنة** قابل للتخصيص
- **حد السرعة** للتحكم في استهلاك الإنترنت
- **التحقق من checksums** للتأكد من سلامة الملفات
- **إعادة المحاولة التلقائية** عند الفشل

### 📊 **مراقبة وإدارة:**
- **شريط تقدم مفصل** مع النسبة المئوية
- **سجل شامل** لجميع العمليات
- **حفظ وتحميل الإعدادات** تلقائياً
- **إيقاف وإستئناف** التحميل

---

## 🚀 **التشغيل السريع**

### **الطريقة الأسهل:**
```bash
run_depot_downloader.bat
```

### **أو يدوياً:**
```bash
python steam_depot_downloader.py
```

---

## 📋 **المتطلبات**

### **النظام:**
- **Windows 10/11** (مُحسن لـ Windows)
- **Python 3.7+**
- **DepotDownloader.dll** (سيتم تحميله تلقائياً)

### **المكتبات:**
- **tkinter** (مدمجة مع Python)
- **requests** (للتحميل)
- **json** (مدمجة مع Python)

---

## 🎮 **كيفية الاستخدام**

### **1. إعداد التطبيق:**
1. **أدخل App ID** للعبة أو التطبيق
2. **اختر Depot ID** المطلوب
3. **حدد مسار التحميل**

### **2. تسجيل الدخول (اختياري):**
- **للمحتوى العام**: لا حاجة لتسجيل الدخول
- **للمحتوى الخاص**: أدخل اسم المستخدم وكلمة المرور

### **3. بدء التحميل:**
- **اضغط "بدء التحميل"**
- **راقب التقدم** في شريط التقدم
- **تابع السجل** للتفاصيل

### **4. إدارة التحميل:**
- **إيقاف مؤقت**: زر "إيقاف"
- **التحقق من الملفات**: زر "التحقق"
- **فتح مجلد التحميل**: من الإعدادات

---

## 🔧 **الإعدادات المتقدمة**

### **أداء التحميل:**
- **عدد الاتصالات**: 1-16 (افتراضي: 8)
- **حد السرعة**: 0-100 MB/s (0 = بلا حدود)

### **خيارات الجودة:**
- ✅ **التحقق من checksums**
- ✅ **إعادة المحاولة التلقائية**
- ❌ **الاحتفاظ بالملفات المؤقتة**

---

## 📊 **معلومات تقنية**

### **الأمان:**
- **لا يحفظ كلمات المرور** في ملفات
- **اتصال مشفر** مع خوادم Steam
- **التحقق من سلامة الملفات** تلقائياً

### **الأداء:**
- **تحميل متعدد الخيوط** للسرعة القصوى
- **استهلاك ذاكرة منخفض**
- **واجهة مستجيبة** أثناء التحميل

---

## 🛠️ **استكشاف الأخطاء**

### **مشاكل شائعة:**

#### **"خطأ في تسجيل الدخول"**
- تأكد من صحة اسم المستخدم وكلمة المرور
- تحقق من تفعيل Steam Guard

#### **"فشل في التحميل"**
- تحقق من اتصال الإنترنت
- تأكد من صحة App ID و Depot ID
- جرب تقليل عدد الاتصالات المتزامنة

#### **"مجلد التحميل غير متاح"**
- تأكد من وجود مساحة كافية
- تحقق من صلاحيات الكتابة

---

## 🎯 **أمثلة عملية**

### **تحميل Counter-Strike: Global Offensive:**
```
App ID: 730
Depot ID: 731 (Windows Content)
Branch: public
```

### **تحميل إصدار Beta:**
```
App ID: [App ID]
Depot ID: [Depot ID]
Branch: beta
Username: [اسم المستخدم]
Password: [كلمة المرور]
```

---

## 🔄 **التطويرات المستقبلية**

### **ميزات مخططة:**
- [ ] **ربط مع Steam API** للبحث الحقيقي
- [ ] **تحميل متعدد الملفات** في نفس الوقت
- [ ] **جدولة التحميل** لأوقات محددة
- [ ] **إشعارات سطح المكتب** عند الانتهاء
- [ ] **دعم أنظمة Linux و macOS**

---

## 🤝 **المساهمة**

### **للمطورين:**
- **الكود مفتوح المصدر** وقابل للتعديل
- **مكتوب بـ Python** مع tkinter
- **موثق بالتفصيل** لسهولة الفهم

### **للمستخدمين:**
- **اقتراح ميزات جديدة**
- **الإبلاغ عن الأخطاء**
- **مشاركة التجربة**

---

## 📄 **الترخيص**

هذا المشروع مطور للاستخدام الشخصي والتعليمي.
يرجى احترام شروط خدمة Steam عند الاستخدام.

---

## 📞 **الدعم**

### **للمساعدة:**
- **اقرأ هذا الدليل أولاً**
- **تحقق من السجل** للأخطاء
- **جرب الإعدادات الافتراضية**

### **للتطوير:**
- **الكود موثق بالتفصيل**
- **استخدام مكتبات Python القياسية**
- **تصميم مودولي قابل للتوسع**

---

**🎉 استمتع بتحميل محتوى Steam بسهولة وسرعة!**

**🎮 Steam Depot Downloader Pro - أداتك المتقدمة لتحميل محتوى Steam!**

---

## 🚀 **ابدأ الآن:**

```bash
# شغل التطبيق
run_depot_downloader.bat

# أو
python steam_depot_downloader.py
```
