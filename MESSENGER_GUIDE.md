# 📱 دليل مرسل الرسائل الشبكي

## 🌟 **مقدمة**

مرسل الرسائل الشبكي هو تطبيق يتيح لك إرسال واستقبال الرسائل بين الأجهزة المتصلة بنفس الشبكة المحلية باستخدام IP Address.

---

## 🚀 **التشغيل السريع**

### **الطريقة الأسهل:**
```bash
run_messenger.bat
```

### **أو يدوياً:**
```bash
python network_messenger.py
```

---

## 🎮 **كيفية الاستخدام**

### **📤 إرسال رسالة:**

#### **1. تعبئة بيانات المستقبل:**
- **🌐 IP Address**: أدخل IP الجهاز المستهدف (مثل *************)
- **📡 Port**: اتركه كما هو (9999) أو غيره حسب الحاجة

#### **2. كتابة الرسالة:**
- **👤 اسم المرسل**: اكتب اسمك
- **📝 عنوان الرسالة**: اكتب عنوان مختصر
- **💬 نص الرسالة**: اكتب محتوى رسالتك

#### **3. الإرسال:**
- اضغط **"📤 إرسال الرسالة"**
- انتظر رسالة التأكيد

### **📥 استقبال الرسائل:**

#### **التطبيق يستقبل الرسائل تلقائياً:**
- **🟢 يستمع للرسائل...** - يعني التطبيق جاهز
- **الرسائل تظهر فوراً** في تبويب "الرسائل المستقبلة"

#### **عرض الرسائل:**
- **انقر مرتين** على أي رسالة لعرض التفاصيل
- **أو اضغط "👁️ عرض الرسالة"**

#### **الرد على الرسائل:**
- **اضغط "↩️ رد على الرسالة"**
- **سيتم تعبئة بيانات المرسل تلقائياً**

### **🔍 اكتشاف الأجهزة:**

#### **فحص الشبكة:**
- **اذهب لتبويب "الأجهزة المتصلة"**
- **اضغط "🔍 فحص الشبكة"**
- **انتظر حتى اكتمال الفحص**

#### **إرسال لجهاز مكتشف:**
- **انقر مرتين** على أي جهاز من القائمة
- **سيتم تعبئة IP تلقائياً**
- **اكتب رسالتك وأرسل**

---

## 🛠️ **الميزات المتقدمة**

### **🧪 اختبار الاتصال:**
- **قبل الإرسال**: اضغط "🧪 اختبار الاتصال"
- **يتحقق من وصول الجهاز ووجود المرسل**

### **📋 إدارة الرسائل:**
- **📋 نسخ المحتوى**: نسخ الرسالة للحافظة
- **🗑️ حذف الرسالة**: حذف رسالة محددة
- **🧹 مسح الكل**: حذف جميع الرسائل

### **🔍 فحص حالة المرسل:**
- **في تبويب الأجهزة**: اضغط "🔍 فحص المرسل"
- **يخبرك إذا كان التطبيق يعمل على الجهاز**

---

## 📊 **معلومات تقنية**

### **🌐 الشبكة:**
- **المنفذ الافتراضي**: 9999
- **البروتوكول**: TCP
- **التشفير**: لا (رسائل واضحة)

### **📱 التوافق:**
- **نظام التشغيل**: Windows, Linux, macOS
- **Python**: 3.7+
- **المكتبات**: مدمجة مع Python

### **🔒 الأمان:**
- **الشبكة المحلية فقط**: لا يعمل عبر الإنترنت
- **لا يحفظ الرسائل**: تختفي عند إغلاق التطبيق
- **لا توجد كلمات مرور**: أي شخص يمكنه الإرسال

---

## 🔧 **استكشاف الأخطاء**

### **❌ "رفض الاتصال"**
**السبب**: التطبيق لا يعمل على الجهاز المستهدف
**الحل**: 
- تأكد من تشغيل المرسل على الجهاز المستهدف
- تحقق من IP Address

### **❌ "انتهت مهلة الاتصال"**
**السبب**: الجهاز غير متصل أو بطيء
**الحل**:
- تحقق من اتصال الجهاز بالشبكة
- جرب ping للجهاز أولاً

### **❌ "الجهاز غير متاح"**
**السبب**: IP خاطئ أو الجهاز مغلق
**الحل**:
- تأكد من صحة IP Address
- تأكد من تشغيل الجهاز

### **🔴 "خطأ في الاستماع"**
**السبب**: المنفذ مستخدم من تطبيق آخر
**الحل**:
- أغلق التطبيقات الأخرى التي تستخدم المنفذ
- أو غير المنفذ في الكود

---

## 💡 **نصائح للاستخدام الأمثل**

### **✅ افعل:**
- **شغل التطبيق على جميع الأجهزة** التي تريد التواصل معها
- **استخدم أسماء واضحة** للمرسل
- **اكتب عناوين مفيدة** للرسائل
- **اختبر الاتصال** قبل إرسال رسائل مهمة

### **❌ تجنب:**
- **لا تستخدم على شبكات عامة** (غير آمن)
- **لا تعتمد عليه للرسائل المهمة** (قد تضيع)
- **لا ترسل معلومات حساسة** (غير مشفر)

---

## 🌟 **أمثلة عملية**

### **📱 إرسال رسالة بسيطة:**
```
IP المستقبل: *************
اسم المرسل: أحمد
العنوان: مرحبا
المحتوى: كيف حالك؟ هل تريد الذهاب للمطعم؟
```

### **💼 رسالة عمل:**
```
IP المستقبل: ************
اسم المرسل: مدير المشروع
العنوان: اجتماع طارئ
المحتوى: اجتماع طارئ في قاعة الاجتماعات الساعة 3 عصراً
```

### **🎮 رسالة لعب:**
```
IP المستقبل: ************
اسم المرسل: صديقك
العنوان: دعوة لعب
المحتوى: هل تريد لعب لعبة جماعية؟ انضم للخادم ************
```

---

## 🔄 **التطويرات المستقبلية**

### **ميزات مخططة:**
- [ ] **تشفير الرسائل** للأمان
- [ ] **حفظ الرسائل** في ملفات
- [ ] **إرسال ملفات** مع الرسائل
- [ ] **مجموعات الدردشة** الجماعية
- [ ] **إشعارات صوتية** للرسائل الجديدة
- [ ] **واجهة ويب** للاستخدام من المتصفح

---

## 🤝 **المساهمة**

### **تطوير الميزات:**
- **اقتراح ميزات جديدة**
- **الإبلاغ عن الأخطاء**
- **تحسين الواجهة**

### **الاختبار:**
- **اختبار على شبكات مختلفة**
- **اختبار مع أجهزة متنوعة**
- **اختبار الأداء**

---

## 📞 **الدعم**

### **للمساعدة:**
- **اقرأ هذا الدليل أولاً**
- **جرب الحلول المقترحة**
- **تحقق من اتصال الشبكة**

### **للتطوير:**
- **الكود مفتوح المصدر**
- **يمكن تعديله حسب الحاجة**
- **مكتوب بـ Python البسيط**

---

**🎉 استمتع بالتواصل مع أجهزتك عبر الشبكة المحلية!**

**📱 مرسل الرسائل الشبكي - تواصل بسيط وفعال!**
