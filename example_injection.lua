-- مثال على كود Lua للحقن
-- يمكن استخدام هذا الكود كمثال للحقن في ملفات العبة

-- إضافة رسالة ترحيب
print("تم حقن الكود بنجاح!")

-- مثال على تعديل متغير
local custom_variable = "قيمة مخصصة"

-- مثال على دالة مخصصة
function custom_function()
    print("هذه دالة مخصصة تم حقنها")
    return true
end

-- مثال على hook لدالة موجودة
local original_function = some_game_function
function some_game_function(...)
    print("تم استدعاء الدالة الأصلية")
    return original_function(...)
end

-- إضافة متغيرات عامة
_G.INJECTED_CODE = true
_G.INJECTION_TIME = os.time()

print("تم تحميل الكود المحقون في:", os.date())
