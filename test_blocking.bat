@echo off
echo ========================================
echo    اختبار فعالية حظر الأجهزة
echo ========================================
echo.

REM التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ تم تشغيل الاختبار بصلاحيات المدير
    echo.
) else (
    echo ⚠️  يتطلب صلاحيات المدير للاختبار الكامل
    echo جاري طلب صلاحيات المدير...
    echo.
    
    REM إعادة تشغيل بصلاحيات المدير
    powershell -Command "Start-Process cmd -ArgumentList '/c cd /d \"%~dp0\" && \"%~f0\"' -Verb RunAs"
    exit /b
)

REM تشغيل أداة الاختبار
echo جاري تشغيل أداة اختبار الحظر...
echo.
python test_blocking.py %1

echo.
echo انتهى الاختبار
pause
