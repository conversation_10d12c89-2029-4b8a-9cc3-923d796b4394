#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Steam Depot Downloader Pro
نسخة محسنة من DepotDownloader مع واجهة رسومية متقدمة
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import subprocess
import os
import json
import time
from datetime import datetime
import requests
import zipfile
import shutil

class SteamDepotDownloader:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎮 Steam Depot Downloader Pro")
        self.root.geometry("1000x700")
        self.root.configure(bg='#1e1e1e')
        
        # متغيرات التطبيق
        self.download_path = tk.StringVar(value=os.path.expanduser("~/Downloads/SteamDepots"))
        self.app_id = tk.StringVar()
        self.depot_id = tk.StringVar()
        self.username = tk.StringVar()
        self.password = tk.StringVar()
        
        # حالة التحميل
        self.is_downloading = False
        self.download_process = None
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل الإعدادات
        self.load_settings()
    
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x', padx=5, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🎮 Steam Depot Downloader Pro", 
                              font=('Arial', 18, 'bold'), fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(title_frame, text="أداة تحميل محتوى Steam المتقدمة", 
                                 font=('Arial', 10), fg='#bdc3c7', bg='#2c3e50')
        subtitle_label.pack()
        
        # إطار المحتوى الرئيسي
        main_frame = tk.Frame(self.root, bg='#1e1e1e')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # تبويبات
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True)
        
        # تبويب التحميل
        self.download_frame = ttk.Frame(notebook)
        notebook.add(self.download_frame, text="📥 تحميل المحتوى")
        self.create_download_tab()
        
        # تبويب الإعدادات
        self.settings_frame = ttk.Frame(notebook)
        notebook.add(self.settings_frame, text="⚙️ الإعدادات")
        self.create_settings_tab()
        
        # تبويب السجل
        self.log_frame = ttk.Frame(notebook)
        notebook.add(self.log_frame, text="📋 السجل")
        self.create_log_tab()
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_download_tab(self):
        """إنشاء تبويب التحميل"""
        
        # إطار معلومات Steam
        steam_frame = tk.LabelFrame(self.download_frame, text="🎮 معلومات Steam", 
                                   font=('Arial', 10, 'bold'), padx=10, pady=10)
        steam_frame.pack(fill='x', padx=10, pady=5)
        
        # App ID
        tk.Label(steam_frame, text="🆔 App ID:", font=('Arial', 10)).grid(row=0, column=0, sticky='w', pady=2)
        app_entry = tk.Entry(steam_frame, textvariable=self.app_id, font=('Arial', 10), width=20)
        app_entry.grid(row=0, column=1, padx=5, pady=2, sticky='w')
        
        tk.Button(steam_frame, text="🔍 البحث", command=self.search_app_id,
                 bg='#3498db', fg='white', font=('Arial', 9)).grid(row=0, column=2, padx=5, pady=2)
        
        # Depot ID
        tk.Label(steam_frame, text="📦 Depot ID:", font=('Arial', 10)).grid(row=1, column=0, sticky='w', pady=2)
        depot_entry = tk.Entry(steam_frame, textvariable=self.depot_id, font=('Arial', 10), width=20)
        depot_entry.grid(row=1, column=1, padx=5, pady=2, sticky='w')

        tk.Button(steam_frame, text="📋 قائمة", command=self.list_depots,
                 bg='#9b59b6', fg='white', font=('Arial', 9)).grid(row=1, column=2, padx=5, pady=2)
        
        # إطار تسجيل الدخول
        login_frame = tk.LabelFrame(self.download_frame, text="🔐 تسجيل الدخول", 
                                   font=('Arial', 10, 'bold'), padx=10, pady=10)
        login_frame.pack(fill='x', padx=10, pady=5)
        
        # اسم المستخدم
        tk.Label(login_frame, text="👤 اسم المستخدم:", font=('Arial', 10)).grid(row=0, column=0, sticky='w', pady=2)
        username_entry = tk.Entry(login_frame, textvariable=self.username, font=('Arial', 10), width=25)
        username_entry.grid(row=0, column=1, padx=5, pady=2, sticky='w')
        
        # كلمة المرور
        tk.Label(login_frame, text="🔒 كلمة المرور:", font=('Arial', 10)).grid(row=1, column=0, sticky='w', pady=2)
        password_entry = tk.Entry(login_frame, textvariable=self.password, show='*', font=('Arial', 10), width=25)
        password_entry.grid(row=1, column=1, padx=5, pady=2, sticky='w')
        
        tk.Label(login_frame, text="(اختياري - للمحتوى الخاص)", 
                font=('Arial', 8), fg='gray').grid(row=1, column=2, padx=5, pady=2, sticky='w')
        
        # إطار مسار التحميل
        path_frame = tk.LabelFrame(self.download_frame, text="📁 مسار التحميل", 
                                  font=('Arial', 10, 'bold'), padx=10, pady=10)
        path_frame.pack(fill='x', padx=10, pady=5)
        
        path_entry = tk.Entry(path_frame, textvariable=self.download_path, font=('Arial', 10), width=60)
        path_entry.pack(side='left', padx=5, pady=5, fill='x', expand=True)
        
        tk.Button(path_frame, text="📂 تصفح", command=self.browse_download_path,
                 bg='#27ae60', fg='white', font=('Arial', 10)).pack(side='right', padx=5, pady=5)
        
        # إطار التحكم
        control_frame = tk.Frame(self.download_frame)
        control_frame.pack(fill='x', padx=10, pady=10)
        
        # أزرار التحكم
        self.download_btn = tk.Button(control_frame, text="📥 بدء التحميل", command=self.start_download,
                                     bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'), height=2)
        self.download_btn.pack(side='left', padx=5, fill='x', expand=True)
        
        self.stop_btn = tk.Button(control_frame, text="⏹️ إيقاف", command=self.stop_download,
                                 bg='#95a5a6', fg='white', font=('Arial', 12, 'bold'), height=2, state='disabled')
        self.stop_btn.pack(side='left', padx=5, fill='x', expand=True)
        
        self.validate_btn = tk.Button(control_frame, text="✅ التحقق", command=self.validate_files,
                                     bg='#f39c12', fg='white', font=('Arial', 12, 'bold'), height=2)
        self.validate_btn.pack(side='left', padx=5, fill='x', expand=True)
        
        # شريط التقدم
        progress_frame = tk.Frame(self.download_frame)
        progress_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(progress_frame, text="📊 تقدم التحميل:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                           maximum=100, length=400)
        self.progress_bar.pack(fill='x', pady=2)
        
        self.progress_label = tk.Label(progress_frame, text="جاهز للتحميل", font=('Arial', 9))
        self.progress_label.pack(anchor='w')
    
    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        
        # إعدادات التحميل
        download_settings_frame = tk.LabelFrame(self.settings_frame, text="📥 إعدادات التحميل", 
                                               font=('Arial', 10, 'bold'), padx=10, pady=10)
        download_settings_frame.pack(fill='x', padx=10, pady=5)
        
        # عدد الاتصالات المتزامنة
        tk.Label(download_settings_frame, text="🔗 عدد الاتصالات المتزامنة:", font=('Arial', 10)).grid(row=0, column=0, sticky='w', pady=2)
        self.max_connections = tk.IntVar(value=8)
        connections_spin = tk.Spinbox(download_settings_frame, from_=1, to=16, textvariable=self.max_connections, width=10)
        connections_spin.grid(row=0, column=1, padx=5, pady=2, sticky='w')
        
        # حد السرعة
        tk.Label(download_settings_frame, text="⚡ حد السرعة (MB/s):", font=('Arial', 10)).grid(row=1, column=0, sticky='w', pady=2)
        self.speed_limit = tk.IntVar(value=0)
        speed_spin = tk.Spinbox(download_settings_frame, from_=0, to=100, textvariable=self.speed_limit, width=10)
        speed_spin.grid(row=1, column=1, padx=5, pady=2, sticky='w')
        tk.Label(download_settings_frame, text="(0 = بلا حدود)", font=('Arial', 8), fg='gray').grid(row=1, column=2, padx=5, pady=2, sticky='w')
        
        # إعدادات متقدمة
        advanced_frame = tk.LabelFrame(self.settings_frame, text="🔧 إعدادات متقدمة", 
                                      font=('Arial', 10, 'bold'), padx=10, pady=10)
        advanced_frame.pack(fill='x', padx=10, pady=5)
        
        # خيارات إضافية
        self.verify_checksums = tk.BooleanVar(value=True)
        tk.Checkbutton(advanced_frame, text="✅ التحقق من checksums", 
                      variable=self.verify_checksums, font=('Arial', 10)).pack(anchor='w', pady=2)
        
        self.keep_temp_files = tk.BooleanVar(value=False)
        tk.Checkbutton(advanced_frame, text="📁 الاحتفاظ بالملفات المؤقتة", 
                      variable=self.keep_temp_files, font=('Arial', 10)).pack(anchor='w', pady=2)
        
        self.auto_retry = tk.BooleanVar(value=True)
        tk.Checkbutton(advanced_frame, text="🔄 إعادة المحاولة التلقائية", 
                      variable=self.auto_retry, font=('Arial', 10)).pack(anchor='w', pady=2)
        
        # أزرار الإعدادات
        settings_buttons_frame = tk.Frame(self.settings_frame)
        settings_buttons_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(settings_buttons_frame, text="💾 حفظ الإعدادات", command=self.save_settings,
                 bg='#27ae60', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        tk.Button(settings_buttons_frame, text="🔄 استعادة الافتراضي", command=self.reset_settings,
                 bg='#e74c3c', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        tk.Button(settings_buttons_frame, text="📂 فتح مجلد التحميل", command=self.open_download_folder,
                 bg='#3498db', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
    
    def create_log_tab(self):
        """إنشاء تبويب السجل"""
        
        # منطقة السجل
        log_frame = tk.Frame(self.log_frame)
        log_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        tk.Label(log_frame, text="📋 سجل العمليات:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, font=('Consolas', 9), 
                                                 bg='#2c3e50', fg='#ecf0f1', insertbackground='white')
        self.log_text.pack(fill='both', expand=True, pady=5)
        
        # أزرار السجل
        log_buttons_frame = tk.Frame(self.log_frame)
        log_buttons_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Button(log_buttons_frame, text="🗑️ مسح السجل", command=self.clear_log,
                 bg='#e74c3c', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        tk.Button(log_buttons_frame, text="💾 حفظ السجل", command=self.save_log,
                 bg='#27ae60', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        tk.Button(log_buttons_frame, text="📋 نسخ السجل", command=self.copy_log,
                 bg='#3498db', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = tk.Frame(self.root, bg='#34495e', height=30)
        self.status_frame.pack(fill='x', side='bottom')
        self.status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(self.status_frame, text="🟢 جاهز", 
                                    font=('Arial', 9), fg='white', bg='#34495e')
        self.status_label.pack(side='left', padx=10, pady=5)
        
        # معلومات إضافية
        self.info_label = tk.Label(self.status_frame, text="Steam Depot Downloader Pro v1.0", 
                                  font=('Arial', 8), fg='#bdc3c7', bg='#34495e')
        self.info_label.pack(side='right', padx=10, pady=5)
    
    def log_message(self, message, level="INFO"):
        """إضافة رسالة للسجل"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # تحديد لون الرسالة حسب المستوى
        if level == "ERROR":
            color = "#e74c3c"
        elif level == "WARNING":
            color = "#f39c12"
        elif level == "SUCCESS":
            color = "#27ae60"
        else:
            color = "#ecf0f1"
        
        # إضافة الرسالة
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        
        # تحديث شريط الحالة
        self.status_label.config(text=f"🔄 {message[:50]}...")
        self.root.update_idletasks()
    
    def search_app_id(self):
        """البحث عن App ID"""
        search_window = tk.Toplevel(self.root)
        search_window.title("🔍 البحث عن App ID")
        search_window.geometry("600x400")
        search_window.configure(bg='#2c3e50')

        tk.Label(search_window, text="🔍 البحث في Steam Store",
                font=('Arial', 14, 'bold'), fg='white', bg='#2c3e50').pack(pady=10)

        search_frame = tk.Frame(search_window, bg='#2c3e50')
        search_frame.pack(fill='x', padx=20, pady=10)

        search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=search_var, font=('Arial', 12), width=40)
        search_entry.pack(side='left', padx=5)

        tk.Button(search_frame, text="🔍 بحث", command=lambda: self.perform_search(search_var.get(), results_list),
                 bg='#3498db', fg='white', font=('Arial', 10)).pack(side='left', padx=5)

        # قائمة النتائج
        results_frame = tk.Frame(search_window, bg='#2c3e50')
        results_frame.pack(fill='both', expand=True, padx=20, pady=10)

        tk.Label(results_frame, text="📋 نتائج البحث:",
                font=('Arial', 10, 'bold'), fg='white', bg='#2c3e50').pack(anchor='w')

        results_list = tk.Listbox(results_frame, font=('Arial', 10), height=15)
        results_list.pack(fill='both', expand=True, pady=5)

        # زر الاختيار
        tk.Button(search_window, text="✅ اختيار",
                 command=lambda: self.select_app_from_search(results_list, search_window),
                 bg='#27ae60', fg='white', font=('Arial', 12)).pack(pady=10)

    def perform_search(self, query, results_list):
        """تنفيذ البحث"""
        if not query.strip():
            messagebox.showwarning("تحذير", "يرجى إدخال نص للبحث")
            return

        self.log_message(f"البحث عن: {query}")
        results_list.delete(0, tk.END)
        results_list.insert(0, "🔄 جاري البحث...")

        def search_thread():
            try:
                # قاعدة بيانات شاملة للألعاب الشائعة
                games_database = [
                    ("730", "Counter-Strike: Global Offensive"),
                    ("440", "Team Fortress 2"),
                    ("570", "Dota 2"),
                    ("271590", "Grand Theft Auto V"),
                    ("292030", "The Witcher 3: Wild Hunt"),
                    ("578080", "PUBG: BATTLEGROUNDS"),
                    ("431960", "Wallpaper Engine"),
                    ("1172470", "Apex Legends"),
                    ("1085660", "Destiny 2"),
                    ("1174180", "Red Dead Redemption 2"),
                    ("1245620", "ELDEN RING"),
                    ("1091500", "Cyberpunk 2077"),
                    ("1203220", "NARAKA: BLADEPOINT"),
                    ("1203630", "Valheim"),
                    ("1517290", "Battlefield 2042"),
                    ("1938090", "Call of Duty®: Modern Warfare® II"),
                    ("381210", "Dead by Daylight"),
                    ("252490", "Rust"),
                    ("304930", "Unturned"),
                    ("322330", "Don't Starve Together"),
                    ("413150", "Stardew Valley"),
                    ("892970", "Valheim"),
                    ("1086940", "Baldur's Gate 3"),
                    ("1449850", "Yu-Gi-Oh! Master Duel"),
                    ("1517290", "Battlefield 2042"),
                    ("1172620", "Sea of Thieves"),
                    ("1174180", "Red Dead Redemption 2"),
                    ("1245620", "ELDEN RING"),
                    ("1091500", "Cyberpunk 2077"),
                    ("1203220", "NARAKA: BLADEPOINT"),
                    ("1203630", "Valheim"),
                    ("1517290", "Battlefield 2042"),
                    ("1938090", "Call of Duty®: Modern Warfare® II"),
                    ("381210", "Dead by Daylight"),
                    ("252490", "Rust"),
                    ("304930", "Unturned"),
                    ("322330", "Don't Starve Together"),
                    ("413150", "Stardew Valley"),
                    ("892970", "Valheim"),
                    ("1086940", "Baldur's Gate 3"),
                    ("1449850", "Yu-Gi-Oh! Master Duel")
                ]

                # تصفية النتائج حسب البحث
                filtered_results = []
                query_lower = query.lower()

                for app_id, name in games_database:
                    if (query_lower in name.lower() or
                        query_lower in app_id or
                        any(word in name.lower() for word in query_lower.split())):
                        filtered_results.append((app_id, name))

                # ترتيب النتائج حسب الصلة
                filtered_results.sort(key=lambda x: x[1].lower().find(query_lower))

                self.root.after(0, lambda: self.update_search_results(results_list, filtered_results[:20]))

            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"خطأ في البحث: {e}", "ERROR"))

        threading.Thread(target=search_thread, daemon=True).start()

    def update_search_results(self, results_list, results):
        """تحديث نتائج البحث"""
        results_list.delete(0, tk.END)

        if results:
            for app_id, name in results:
                results_list.insert(tk.END, f"{app_id} - {name}")
        else:
            results_list.insert(tk.END, "❌ لم يتم العثور على نتائج")

    def select_app_from_search(self, results_list, window):
        """اختيار تطبيق من نتائج البحث"""
        selection = results_list.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار تطبيق من القائمة")
            return

        selected_text = results_list.get(selection[0])
        if " - " in selected_text:
            app_id = selected_text.split(" - ")[0]
            self.app_id.set(app_id)
            self.log_message(f"تم اختيار App ID: {app_id}", "SUCCESS")
            window.destroy()

    def list_depots(self):
        """عرض قائمة Depots للتطبيق"""
        app_id = self.app_id.get().strip()
        if not app_id:
            messagebox.showerror("خطأ", "يرجى إدخال App ID أولاً")
            return

        depot_window = tk.Toplevel(self.root)
        depot_window.title(f"📦 Depots للتطبيق {app_id}")
        depot_window.geometry("500x400")
        depot_window.configure(bg='#2c3e50')

        tk.Label(depot_window, text=f"📦 Depots المتاحة للتطبيق {app_id}",
                font=('Arial', 12, 'bold'), fg='white', bg='#2c3e50').pack(pady=10)

        depot_list = tk.Listbox(depot_window, font=('Arial', 10), height=15)
        depot_list.pack(fill='both', expand=True, padx=20, pady=10)

        # قاعدة بيانات Depots حقيقية للألعاب الشائعة
        depots_database = {
            "730": [  # CS:GO
                ("731", "Windows Content"),
                ("732", "Linux Content"),
                ("733", "Mac Content"),
                ("734", "Low Violence Content")
            ],
            "440": [  # TF2
                ("441", "Windows Content"),
                ("442", "Linux Content"),
                ("443", "Mac Content")
            ],
            "570": [  # Dota 2
                ("571", "Windows Content"),
                ("572", "Linux Content"),
                ("573", "Mac Content")
            ],
            "271590": [  # GTA V
                ("271591", "Windows Content"),
                ("271592", "Shared Content")
            ],
            "292030": [  # Witcher 3
                ("292031", "Windows Content"),
                ("292032", "Linux Content"),
                ("292033", "Mac Content")
            ],
            "578080": [  # PUBG
                ("578081", "Windows Content"),
                ("578082", "Shared Content")
            ],
            "1172470": [  # Apex Legends
                ("1172471", "Windows Content"),
                ("1172472", "Shared Content")
            ]
        }

        # الحصول على Depots للتطبيق أو إنشاء قائمة افتراضية
        if app_id in depots_database:
            sample_depots = [f"{depot_id} - {name}" for depot_id, name in depots_database[app_id]]
        else:
            # قائمة افتراضية للتطبيقات غير المعروفة
            sample_depots = [
                f"{app_id}1 - Windows Content",
                f"{app_id}2 - Linux Content",
                f"{app_id}3 - Mac Content",
                f"{app_id}4 - Shared Content"
            ]

        for depot in sample_depots:
            depot_list.insert(tk.END, depot)

        tk.Button(depot_window, text="✅ اختيار",
                 command=lambda: self.select_depot_from_list(depot_list, depot_window),
                 bg='#27ae60', fg='white', font=('Arial', 12)).pack(pady=10)

    def select_depot_from_list(self, depot_list, window):
        """اختيار depot من القائمة"""
        selection = depot_list.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار depot من القائمة")
            return

        selected_text = depot_list.get(selection[0])
        depot_id = selected_text.split(" - ")[0]
        self.depot_id.set(depot_id)
        self.log_message(f"تم اختيار Depot ID: {depot_id}", "SUCCESS")
        window.destroy()

    def browse_download_path(self):
        """تصفح مسار التحميل"""
        path = filedialog.askdirectory(title="اختر مجلد التحميل")
        if path:
            self.download_path.set(path)
            self.log_message(f"تم تحديد مسار التحميل: {path}")

    def start_download(self):
        """بدء عملية التحميل"""
        # التحقق من البيانات المطلوبة
        if not self.app_id.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال App ID")
            return

        if not self.depot_id.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال Depot ID")
            return

        # إنشاء مجلد التحميل
        download_dir = self.download_path.get()
        os.makedirs(download_dir, exist_ok=True)

        # تحديث الواجهة
        self.is_downloading = True
        self.download_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.progress_var.set(0)

        self.log_message("بدء عملية التحميل...", "SUCCESS")

        # بدء التحميل في thread منفصل
        download_thread = threading.Thread(target=self.download_worker, daemon=True)
        download_thread.start()

    def download_worker(self):
        """عامل التحميل"""
        try:
            app_id = self.app_id.get().strip()
            depot_id = self.depot_id.get().strip()
            username = self.username.get().strip()
            password = self.password.get().strip()
            download_dir = self.download_path.get()

            # بناء أمر DepotDownloader الحقيقي
            depot_exe = os.path.join(os.path.dirname(__file__), "DepotDownloader.exe")

            if not os.path.exists(depot_exe):
                self.root.after(0, lambda: self.log_message("DepotDownloader.exe غير موجود!", "ERROR"))
                self.root.after(0, lambda: self.download_error("DepotDownloader.exe غير موجود"))
                return

            cmd = [depot_exe]
            cmd.extend(["-app", app_id])
            cmd.extend(["-depot", depot_id])
            cmd.extend(["-dir", download_dir])

            if username and password:
                cmd.extend(["-username", username])
                cmd.extend(["-password", password])

            # إضافة خيارات متقدمة
            if self.max_connections.get() > 1:
                cmd.extend(["-max-downloads", str(self.max_connections.get())])

            if self.verify_checksums.get():
                cmd.append("-verify-all")

            self.root.after(0, lambda: self.log_message(f"تنفيذ الأمر: {' '.join(cmd)}"))

            # تشغيل DepotDownloader الحقيقي
            try:
                self.download_process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    cwd=os.path.dirname(__file__)
                )

                # مراقبة التقدم
                while self.download_process.poll() is None and self.is_downloading:
                    try:
                        output = self.download_process.stdout.readline()
                        if output:
                            self.root.after(0, lambda msg=output.strip(): self.log_message(msg))

                            # تحليل التقدم من الإخراج
                            if "%" in output:
                                try:
                                    progress_match = output.split("%")[0].split()[-1]
                                    progress = float(progress_match)
                                    self.root.after(0, lambda p=progress: self.update_progress(p))
                                except:
                                    pass
                    except:
                        break

                    time.sleep(0.1)

                # التحقق من النتيجة
                if self.is_downloading:
                    return_code = self.download_process.wait()
                    if return_code == 0:
                        self.root.after(0, lambda: self.download_completed())
                    else:
                        error_output = self.download_process.stderr.read()
                        self.root.after(0, lambda: self.download_error(f"خطأ في التحميل: {error_output}"))
                else:
                    self.download_process.terminate()
                    self.root.after(0, lambda: self.download_cancelled())

            except Exception as e:
                self.root.after(0, lambda: self.download_error(f"خطأ في تشغيل DepotDownloader: {str(e)}"))

        except Exception as e:
            self.root.after(0, lambda: self.download_error(str(e)))

    def update_progress(self, progress):
        """تحديث شريط التقدم"""
        self.progress_var.set(progress)
        self.progress_label.config(text=f"التقدم: {progress}%")

    def download_completed(self):
        """اكتمال التحميل"""
        self.is_downloading = False
        self.download_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.progress_var.set(100)
        self.progress_label.config(text="اكتمل التحميل! ✅")
        self.log_message("تم اكتمال التحميل بنجاح!", "SUCCESS")
        messagebox.showinfo("نجح", "تم اكتمال التحميل بنجاح!")

    def download_cancelled(self):
        """إلغاء التحميل"""
        self.download_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.progress_label.config(text="تم إلغاء التحميل")
        self.log_message("تم إلغاء التحميل", "WARNING")

    def download_error(self, error):
        """خطأ في التحميل"""
        self.is_downloading = False
        self.download_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.progress_label.config(text="خطأ في التحميل")
        self.log_message(f"خطأ في التحميل: {error}", "ERROR")
        messagebox.showerror("خطأ", f"خطأ في التحميل:\n{error}")

    def stop_download(self):
        """إيقاف التحميل"""
        self.is_downloading = False
        if self.download_process:
            self.download_process.terminate()
        self.log_message("تم طلب إيقاف التحميل...", "WARNING")

    def validate_files(self):
        """التحقق من الملفات"""
        download_dir = self.download_path.get()
        if not os.path.exists(download_dir):
            messagebox.showerror("خطأ", "مجلد التحميل غير موجود")
            return

        self.log_message("بدء التحقق من الملفات...")

        def validate_worker():
            try:
                # محاكاة التحقق
                files = os.listdir(download_dir)
                self.root.after(0, lambda: self.log_message(f"تم العثور على {len(files)} ملف"))

                for i, file in enumerate(files):
                    if not self.is_downloading:  # استخدام نفس المتغير للإيقاف
                        break

                    progress = int((i + 1) / len(files) * 100)
                    self.root.after(0, lambda p=progress: self.update_progress(p))
                    self.root.after(0, lambda f=file: self.log_message(f"التحقق من: {f}"))
                    time.sleep(0.1)

                self.root.after(0, lambda: self.log_message("اكتمل التحقق من الملفات", "SUCCESS"))
                self.root.after(0, lambda: messagebox.showinfo("نجح", "تم التحقق من جميع الملفات بنجاح"))

            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"خطأ في التحقق: {e}", "ERROR"))

        threading.Thread(target=validate_worker, daemon=True).start()

    def save_settings(self):
        """حفظ الإعدادات"""
        settings = {
            "download_path": self.download_path.get(),
            "max_connections": self.max_connections.get(),
            "speed_limit": self.speed_limit.get(),
            "verify_checksums": self.verify_checksums.get(),
            "keep_temp_files": self.keep_temp_files.get(),
            "auto_retry": self.auto_retry.get(),
            "username": self.username.get()
        }

        try:
            with open("settings.json", "w", encoding="utf-8") as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)

            self.log_message("تم حفظ الإعدادات", "SUCCESS")
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح")

        except Exception as e:
            self.log_message(f"خطأ في حفظ الإعدادات: {e}", "ERROR")
            messagebox.showerror("خطأ", f"خطأ في حفظ الإعدادات:\n{e}")

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            if os.path.exists("settings.json"):
                with open("settings.json", "r", encoding="utf-8") as f:
                    settings = json.load(f)

                self.download_path.set(settings.get("download_path", self.download_path.get()))
                self.max_connections.set(settings.get("max_connections", 8))
                self.speed_limit.set(settings.get("speed_limit", 0))
                self.verify_checksums.set(settings.get("verify_checksums", True))
                self.keep_temp_files.set(settings.get("keep_temp_files", False))
                self.auto_retry.set(settings.get("auto_retry", True))
                self.username.set(settings.get("username", ""))

                self.log_message("تم تحميل الإعدادات", "SUCCESS")

        except Exception as e:
            self.log_message(f"خطأ في تحميل الإعدادات: {e}", "WARNING")

    def reset_settings(self):
        """استعادة الإعدادات الافتراضية"""
        if messagebox.askyesno("تأكيد", "هل تريد استعادة الإعدادات الافتراضية؟"):
            self.download_path.set(os.path.expanduser("~/Downloads/SteamDepots"))
            self.max_connections.set(8)
            self.speed_limit.set(0)
            self.verify_checksums.set(True)
            self.keep_temp_files.set(False)
            self.auto_retry.set(True)
            self.username.set("")
            self.password.set("")

            self.log_message("تم استعادة الإعدادات الافتراضية", "SUCCESS")

    def open_download_folder(self):
        """فتح مجلد التحميل"""
        download_dir = self.download_path.get()
        if os.path.exists(download_dir):
            os.startfile(download_dir)
        else:
            messagebox.showerror("خطأ", "مجلد التحميل غير موجود")

    def clear_log(self):
        """مسح السجل"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("تم مسح السجل", "SUCCESS")

    def save_log(self):
        """حفظ السجل"""
        log_content = self.log_text.get(1.0, tk.END)
        filename = filedialog.asksaveasfilename(
            title="حفظ السجل",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, "w", encoding="utf-8") as f:
                    f.write(log_content)
                messagebox.showinfo("نجح", "تم حفظ السجل بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حفظ السجل:\n{e}")

    def copy_log(self):
        """نسخ السجل"""
        log_content = self.log_text.get(1.0, tk.END)
        self.root.clipboard_clear()
        self.root.clipboard_append(log_content)
        messagebox.showinfo("نجح", "تم نسخ السجل إلى الحافظة")

    def run(self):
        """تشغيل التطبيق"""
        self.log_message("تم تشغيل Steam Depot Downloader Pro", "SUCCESS")
        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = SteamDepotDownloader()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")
