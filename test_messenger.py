#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أداة اختبار مرسل الرسائل الشبكي
Test Network Messenger
"""

import socket
import json
import sys
import time
from datetime import datetime

def get_local_ip():
    """الحصول على IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def test_send_message(target_ip, port=9999):
    """اختبار إرسال رسالة"""
    print(f"📤 اختبار إرسال رسالة إلى {target_ip}:{port}")
    
    # إعداد بيانات الرسالة
    message_data = {
        "sender_name": "أداة الاختبار",
        "title": "رسالة اختبار",
        "content": f"هذه رسالة اختبار تم إرسالها في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\nإذا وصلتك هذه الرسالة، فإن المرسل يعمل بشكل صحيح! ✅",
        "sent_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "sender_ip": get_local_ip()
    }
    
    try:
        # إنشاء الاتصال
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client_socket.settimeout(10)
        
        print(f"🔄 جاري الاتصال بـ {target_ip}...")
        client_socket.connect((target_ip, port))
        print(f"✅ تم الاتصال بنجاح!")
        
        # إرسال البيانات
        data = json.dumps(message_data, ensure_ascii=False)
        client_socket.send(data.encode('utf-8'))
        print(f"📨 تم إرسال الرسالة...")
        
        # انتظار تأكيد الاستلام
        response = client_socket.recv(1024).decode('utf-8')
        response_data = json.loads(response)
        
        client_socket.close()
        
        if response_data.get('status') == 'received':
            print(f"🎉 نجح الاختبار! تم استلام الرسالة في {response_data.get('time', 'غير معروف')}")
            return True
        else:
            print(f"⚠️ تم إرسال الرسالة ولكن لم يتم تأكيد الاستلام")
            return False
            
    except socket.timeout:
        print(f"❌ انتهت مهلة الاتصال مع {target_ip}")
        print(f"💡 تأكد من:")
        print(f"   • الجهاز متصل بالشبكة")
        print(f"   • تطبيق المرسل يعمل على الجهاز")
        print(f"   • المنفذ {port} مفتوح")
        return False
        
    except ConnectionRefusedError:
        print(f"❌ رفض الجهاز {target_ip} الاتصال")
        print(f"💡 تأكد من تشغيل تطبيق المرسل على الجهاز المستهدف")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في الإرسال: {str(e)}")
        return False

def test_ping(target_ip):
    """اختبار ping للجهاز"""
    print(f"🏓 اختبار ping للجهاز {target_ip}...")
    
    import subprocess
    try:
        result = subprocess.run(['ping', '-n', '1', target_ip], 
                              capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            print(f"✅ الجهاز {target_ip} يستجيب للـ ping")
            return True
        else:
            print(f"❌ الجهاز {target_ip} لا يستجيب للـ ping")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في ping: {e}")
        return False

def test_port(target_ip, port=9999):
    """اختبار المنفذ"""
    print(f"🔌 اختبار المنفذ {port} على {target_ip}...")
    
    try:
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        test_socket.settimeout(3)
        test_socket.connect((target_ip, port))
        test_socket.close()
        
        print(f"✅ المنفذ {port} مفتوح ومتاح")
        return True
        
    except Exception as e:
        print(f"❌ المنفذ {port} غير متاح: {str(e)}")
        return False

def scan_network_for_messengers():
    """فحص الشبكة للبحث عن المرسلين"""
    print(f"🔍 فحص الشبكة للبحث عن تطبيقات المرسل...")
    
    local_ip = get_local_ip()
    network_base = '.'.join(local_ip.split('.')[:-1]) + '.'
    
    print(f"🌐 فحص الشبكة: {network_base}1-254")
    print(f"📡 البحث عن المنفذ: 9999")
    print()
    
    found_messengers = []
    
    for i in range(1, 255):
        ip = network_base + str(i)
        
        # تخطي IP الخاص بنا
        if ip == local_ip:
            continue
        
        try:
            # اختبار سريع للمنفذ
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.settimeout(0.5)
            test_socket.connect((ip, 9999))
            test_socket.close()
            
            print(f"✅ تم العثور على مرسل في {ip}")
            found_messengers.append(ip)
            
        except:
            continue
    
    print()
    if found_messengers:
        print(f"🎉 تم العثور على {len(found_messengers)} مرسل:")
        for ip in found_messengers:
            print(f"   📱 {ip}")
    else:
        print(f"❌ لم يتم العثور على أي مرسل في الشبكة")
    
    return found_messengers

def interactive_test():
    """اختبار تفاعلي"""
    print("=" * 50)
    print("🧪 أداة اختبار مرسل الرسائل الشبكي")
    print("=" * 50)
    print()
    
    # عرض IP المحلي
    local_ip = get_local_ip()
    print(f"🌐 IP الخاص بك: {local_ip}")
    print()
    
    while True:
        print("اختر نوع الاختبار:")
        print("1. 📤 اختبار إرسال رسالة")
        print("2. 🔍 فحص الشبكة للمرسلين")
        print("3. 🏓 اختبار ping لجهاز")
        print("4. 🔌 اختبار منفذ")
        print("5. ❌ خروج")
        print()
        
        choice = input("اختر رقم (1-5): ").strip()
        print()
        
        if choice == "1":
            target_ip = input("أدخل IP الجهاز المستهدف: ").strip()
            if target_ip:
                print()
                test_send_message(target_ip)
            else:
                print("❌ يرجى إدخال IP صحيح")
        
        elif choice == "2":
            scan_network_for_messengers()
        
        elif choice == "3":
            target_ip = input("أدخل IP الجهاز: ").strip()
            if target_ip:
                print()
                test_ping(target_ip)
            else:
                print("❌ يرجى إدخال IP صحيح")
        
        elif choice == "4":
            target_ip = input("أدخل IP الجهاز: ").strip()
            port = input("أدخل رقم المنفذ (افتراضي 9999): ").strip()
            
            if target_ip:
                port = int(port) if port.isdigit() else 9999
                print()
                test_port(target_ip, port)
            else:
                print("❌ يرجى إدخال IP صحيح")
        
        elif choice == "5":
            print("👋 وداعاً!")
            break
        
        else:
            print("❌ اختيار غير صحيح")
        
        print()
        print("-" * 50)
        print()

def main():
    """الوظيفة الرئيسية"""
    if len(sys.argv) > 1:
        # تشغيل مع معاملات
        target_ip = sys.argv[1]
        
        print("=" * 50)
        print("🧪 أداة اختبار مرسل الرسائل الشبكي")
        print("=" * 50)
        print()
        
        print(f"🎯 اختبار الجهاز: {target_ip}")
        print()
        
        # اختبار شامل
        ping_ok = test_ping(target_ip)
        print()
        
        if ping_ok:
            port_ok = test_port(target_ip)
            print()
            
            if port_ok:
                message_ok = test_send_message(target_ip)
                print()
                
                if message_ok:
                    print("🎉 جميع الاختبارات نجحت! المرسل يعمل بشكل مثالي")
                else:
                    print("⚠️ الجهاز متاح ولكن فشل في إرسال الرسالة")
            else:
                print("❌ الجهاز متاح ولكن المرسل لا يعمل")
        else:
            print("❌ الجهاز غير متاح")
        
        print()
        print("=" * 50)
    
    else:
        # تشغيل تفاعلي
        interactive_test()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        input("اضغط Enter للخروج...")
