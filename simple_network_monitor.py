"""
مراقب الشبكة المبسط - نسخة للاختبار
"""
import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import threading
import time
import socket
import psutil

class SimpleNetworkMonitor:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("مراقب الشبكة المبسط")
        self.root.geometry("800x600")
        
        self.devices = {}
        self.monitoring = False
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تبويبات
        notebook = ttk.Notebook(main_frame)
        notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تبويب الأجهزة
        devices_frame = ttk.Frame(notebook)
        notebook.add(devices_frame, text="الأجهزة المتصلة")
        
        # أزرار التحكم
        control_frame = ttk.Frame(devices_frame)
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(control_frame, text="فحص الشبكة", command=self.scan_network).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="اختبار ping", command=self.test_ping).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="معلومات الشبكة", command=self.show_network_info).pack(side=tk.LEFT)
        
        # جدول الأجهزة
        self.devices_tree = ttk.Treeview(devices_frame, columns=("ip", "status"), show="tree headings", height=15)
        self.devices_tree.heading("#0", text="الجهاز")
        self.devices_tree.heading("ip", text="IP Address")
        self.devices_tree.heading("status", text="الحالة")
        
        self.devices_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تبويب مراقبة البيانات
        bandwidth_frame = ttk.Frame(notebook)
        notebook.add(bandwidth_frame, text="مراقبة البيانات")
        
        # أزرار المراقبة
        monitor_control_frame = ttk.Frame(bandwidth_frame)
        monitor_control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(monitor_control_frame, text="بدء المراقبة", command=self.start_monitoring).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(monitor_control_frame, text="إيقاف المراقبة", command=self.stop_monitoring).pack(side=tk.LEFT)
        
        # عرض البيانات
        self.bandwidth_text = tk.Text(bandwidth_frame, height=20, width=60)
        self.bandwidth_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تبويب إدارة الأجهزة
        management_frame = ttk.Frame(notebook)
        notebook.add(management_frame, text="إدارة الأجهزة")
        
        # حظر الأجهزة
        block_frame = ttk.LabelFrame(management_frame, text="حظر الأجهزة", padding="5")
        block_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(block_frame, text="IP Address:").grid(row=0, column=0, sticky=tk.W)
        self.block_ip_var = tk.StringVar()
        self.block_ip_entry = ttk.Entry(block_frame, textvariable=self.block_ip_var, width=20)
        self.block_ip_entry.grid(row=0, column=1, padx=(5, 10))
        
        ttk.Button(block_frame, text="حظر", command=self.block_device).grid(row=0, column=2, padx=(0, 5))
        ttk.Button(block_frame, text="إلغاء الحظر", command=self.unblock_device).grid(row=0, column=3)
        
        # شريط الحالة
        self.status_var = tk.StringVar(value="جاهز")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # إعداد التوسيع
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(0, weight=1)
        devices_frame.columnconfigure(0, weight=1)
        devices_frame.rowconfigure(1, weight=1)
        bandwidth_frame.columnconfigure(0, weight=1)
        bandwidth_frame.rowconfigure(1, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def get_local_ip(self):
        """الحصول على IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "*************"
    
    def ping_host(self, ip):
        """فحص ping للجهاز"""
        try:
            result = subprocess.run(['ping', '-n', '1', '-w', '1000', ip], 
                                  capture_output=True, text=True, timeout=3)
            return result.returncode == 0
        except:
            return False
    
    def scan_network(self):
        """فحص الشبكة"""
        self.status_var.set("جاري فحص الشبكة...")
        
        def run_scan():
            try:
                # مسح الجدول
                for item in self.devices_tree.get_children():
                    self.devices_tree.delete(item)
                
                local_ip = self.get_local_ip()
                network_base = '.'.join(local_ip.split('.')[:-1]) + '.'
                
                # فحص النطاق 1-254
                for i in range(1, 255):
                    ip = network_base + str(i)
                    if self.ping_host(ip):
                        status = "متصل" if ip != local_ip else "جهازك"
                        self.root.after(0, lambda ip=ip, status=status: 
                                      self.devices_tree.insert("", tk.END, text=f"جهاز-{ip.split('.')[-1]}", 
                                                             values=(ip, status)))
                
                self.root.after(0, lambda: self.status_var.set("تم الانتهاء من فحص الشبكة"))
                
            except Exception as e:
                self.root.after(0, lambda: self.status_var.set(f"خطأ في الفحص: {e}"))
        
        scan_thread = threading.Thread(target=run_scan)
        scan_thread.daemon = True
        scan_thread.start()
    
    def test_ping(self):
        """اختبار ping لجوجل"""
        self.status_var.set("جاري اختبار الاتصال...")
        
        def run_ping():
            try:
                result = subprocess.run(['ping', '-n', '4', '*******'], 
                                      capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    self.root.after(0, lambda: self.status_var.set("الاتصال بالإنترنت يعمل بشكل طبيعي"))
                    self.root.after(0, lambda: messagebox.showinfo("نتيجة الاختبار", "الاتصال بالإنترنت يعمل بشكل طبيعي"))
                else:
                    self.root.after(0, lambda: self.status_var.set("مشكلة في الاتصال بالإنترنت"))
                    self.root.after(0, lambda: messagebox.showwarning("نتيجة الاختبار", "مشكلة في الاتصال بالإنترنت"))
                    
            except Exception as e:
                self.root.after(0, lambda: self.status_var.set(f"خطأ في الاختبار: {e}"))
        
        ping_thread = threading.Thread(target=run_ping)
        ping_thread.daemon = True
        ping_thread.start()
    
    def show_network_info(self):
        """عرض معلومات الشبكة"""
        try:
            local_ip = self.get_local_ip()
            
            # الحصول على معلومات الشبكة
            info = f"IP المحلي: {local_ip}\n"
            info += f"نطاق الشبكة: {'.'.join(local_ip.split('.')[:-1])}.0/24\n"
            
            # معلومات إضافية من psutil
            try:
                stats = psutil.net_if_stats()
                addrs = psutil.net_if_addrs()
                
                info += "\nواجهات الشبكة:\n"
                for interface, addresses in addrs.items():
                    if interface in stats and stats[interface].isup:
                        for addr in addresses:
                            if addr.family == 2:  # IPv4
                                info += f"  {interface}: {addr.address}\n"
                                break
            except:
                pass
            
            messagebox.showinfo("معلومات الشبكة", info)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في الحصول على معلومات الشبكة: {e}")
    
    def start_monitoring(self):
        """بدء مراقبة البيانات"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.status_var.set("بدأت مراقبة البيانات")
        
        def monitor():
            try:
                while self.monitoring:
                    # الحصول على إحصائيات الشبكة
                    stats = psutil.net_io_counters()
                    
                    timestamp = time.strftime("%H:%M:%S")
                    info = f"[{timestamp}] "
                    info += f"تحميل: {stats.bytes_recv / 1024 / 1024:.2f} MB | "
                    info += f"رفع: {stats.bytes_sent / 1024 / 1024:.2f} MB\n"
                    
                    self.root.after(0, lambda: self.bandwidth_text.insert(tk.END, info))
                    self.root.after(0, lambda: self.bandwidth_text.see(tk.END))
                    
                    time.sleep(2)
                    
            except Exception as e:
                self.root.after(0, lambda: self.status_var.set(f"خطأ في المراقبة: {e}"))
        
        monitor_thread = threading.Thread(target=monitor)
        monitor_thread.daemon = True
        monitor_thread.start()
    
    def stop_monitoring(self):
        """إيقاف مراقبة البيانات"""
        self.monitoring = False
        self.status_var.set("تم إيقاف مراقبة البيانات")
    
    def block_device(self):
        """حظر جهاز (محاكاة)"""
        ip = self.block_ip_var.get().strip()
        if not ip:
            messagebox.showwarning("تحذير", "يرجى إدخال IP Address")
            return
        
        # هذه محاكاة - في الواقع نحتاج صلاحيات المدير
        response = messagebox.askyesno("تأكيد", f"هل تريد حظر الجهاز {ip}؟\n(هذه محاكاة)")
        if response:
            messagebox.showinfo("تم", f"تم حظر الجهاز {ip} (محاكاة)")
            self.status_var.set(f"تم حظر الجهاز {ip}")
    
    def unblock_device(self):
        """إلغاء حظر جهاز (محاكاة)"""
        ip = self.block_ip_var.get().strip()
        if not ip:
            messagebox.showwarning("تحذير", "يرجى إدخال IP Address")
            return
        
        response = messagebox.askyesno("تأكيد", f"هل تريد إلغاء حظر الجهاز {ip}؟\n(هذه محاكاة)")
        if response:
            messagebox.showinfo("تم", f"تم إلغاء حظر الجهاز {ip} (محاكاة)")
            self.status_var.set(f"تم إلغاء حظر الجهاز {ip}")
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = SimpleNetworkMonitor()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")
