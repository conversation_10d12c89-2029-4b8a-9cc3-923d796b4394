"""
مراقب الشبكة المبسط - نسخة للاختبار
"""
import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import threading
import time
import socket
import psutil

class SimpleNetworkMonitor:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("مراقب الشبكة المبسط")
        self.root.geometry("800x600")
        
        self.devices = {}
        self.monitoring = False
        self.blocked_devices = set()

        # تحميل قائمة الأجهزة المحظورة
        self.load_blocked_devices()

        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تبويبات
        notebook = ttk.Notebook(main_frame)
        notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تبويب الأجهزة
        devices_frame = ttk.Frame(notebook)
        notebook.add(devices_frame, text="الأجهزة المتصلة")
        
        # أزرار التحكم
        control_frame = ttk.Frame(devices_frame)
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(control_frame, text="فحص الشبكة", command=self.scan_network).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="اختبار ping", command=self.test_ping).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="معلومات الشبكة", command=self.show_network_info).pack(side=tk.LEFT)
        
        # جدول الأجهزة
        self.devices_tree = ttk.Treeview(devices_frame, columns=("ip", "mac", "status"), show="tree headings", height=15)
        self.devices_tree.heading("#0", text="اسم الجهاز")
        self.devices_tree.heading("ip", text="IP Address")
        self.devices_tree.heading("mac", text="MAC Address")
        self.devices_tree.heading("status", text="الحالة")

        # تحديد عرض الأعمدة
        self.devices_tree.column("#0", width=200)
        self.devices_tree.column("ip", width=120)
        self.devices_tree.column("mac", width=150)
        self.devices_tree.column("status", width=100)

        self.devices_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # ربط النقر المزدوج بعرض تفاصيل الجهاز
        self.devices_tree.bind("<Double-1>", self.show_device_details)
        
        # تبويب مراقبة البيانات
        bandwidth_frame = ttk.Frame(notebook)
        notebook.add(bandwidth_frame, text="مراقبة البيانات")
        
        # أزرار المراقبة
        monitor_control_frame = ttk.Frame(bandwidth_frame)
        monitor_control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(monitor_control_frame, text="بدء المراقبة", command=self.start_monitoring).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(monitor_control_frame, text="إيقاف المراقبة", command=self.stop_monitoring).pack(side=tk.LEFT)
        
        # عرض البيانات
        self.bandwidth_text = tk.Text(bandwidth_frame, height=20, width=60)
        self.bandwidth_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تبويب إدارة الأجهزة
        management_frame = ttk.Frame(notebook)
        notebook.add(management_frame, text="إدارة الأجهزة")
        
        # حظر الأجهزة
        block_frame = ttk.LabelFrame(management_frame, text="حظر الأجهزة", padding="5")
        block_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(block_frame, text="IP Address:").grid(row=0, column=0, sticky=tk.W)
        self.block_ip_var = tk.StringVar()
        self.block_ip_entry = ttk.Entry(block_frame, textvariable=self.block_ip_var, width=20)
        self.block_ip_entry.grid(row=0, column=1, padx=(5, 10))
        
        ttk.Button(block_frame, text="حظر", command=self.block_device).grid(row=0, column=2, padx=(0, 5))
        ttk.Button(block_frame, text="إلغاء الحظر", command=self.unblock_device).grid(row=0, column=3)

        # قائمة الأجهزة المحظورة
        blocked_list_frame = ttk.LabelFrame(management_frame, text="الأجهزة المحظورة", padding="5")
        blocked_list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        # جدول الأجهزة المحظورة
        self.blocked_tree = ttk.Treeview(blocked_list_frame, columns=("ip", "name", "time"), show="tree headings", height=8)
        self.blocked_tree.heading("#0", text="الحالة")
        self.blocked_tree.heading("ip", text="IP Address")
        self.blocked_tree.heading("name", text="اسم الجهاز")
        self.blocked_tree.heading("time", text="وقت الحظر")

        self.blocked_tree.column("#0", width=80)
        self.blocked_tree.column("ip", width=120)
        self.blocked_tree.column("name", width=150)
        self.blocked_tree.column("time", width=120)

        self.blocked_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # أزرار إدارة الحظر
        blocked_buttons_frame = ttk.Frame(blocked_list_frame)
        blocked_buttons_frame.grid(row=1, column=0, pady=(5, 0))

        ttk.Button(blocked_buttons_frame, text="تحديث القائمة", command=self.refresh_blocked_list).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(blocked_buttons_frame, text="إلغاء حظر المحدد", command=self.unblock_selected).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(blocked_buttons_frame, text="إلغاء حظر الكل", command=self.unblock_all_devices).pack(side=tk.LEFT)
        
        # شريط الحالة
        self.status_var = tk.StringVar(value="جاهز")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # إعداد التوسيع
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(0, weight=1)
        devices_frame.columnconfigure(0, weight=1)
        devices_frame.rowconfigure(1, weight=1)
        bandwidth_frame.columnconfigure(0, weight=1)
        bandwidth_frame.rowconfigure(1, weight=1)
        management_frame.columnconfigure(0, weight=1)
        management_frame.rowconfigure(1, weight=1)
        blocked_list_frame.columnconfigure(0, weight=1)
        blocked_list_frame.rowconfigure(0, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def get_local_ip(self):
        """الحصول على IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "*************"
    
    def ping_host(self, ip):
        """فحص ping للجهاز"""
        try:
            result = subprocess.run(['ping', '-n', '1', '-w', '1000', ip],
                                  capture_output=True, text=True, timeout=3)
            return result.returncode == 0
        except:
            return False

    def get_device_name(self, ip):
        """الحصول على اسم الجهاز"""
        try:
            # محاولة الحصول على hostname
            hostname = socket.gethostbyaddr(ip)[0]
            if hostname and hostname != ip:
                return hostname
        except:
            pass

        try:
            # محاولة استخدام nbtstat للحصول على اسم Windows
            result = subprocess.run(['nbtstat', '-A', ip],
                                  capture_output=True, text=True, timeout=3)

            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if '<00>' in line and 'UNIQUE' in line:
                        # استخراج اسم الجهاز من السطر
                        parts = line.split()
                        if parts:
                            name = parts[0].strip()
                            if name and name != ip and not name.startswith('MAC'):
                                return name
        except:
            pass

        try:
            # محاولة استخدام arp للحصول على معلومات إضافية
            result = subprocess.run(['arp', '-a', ip],
                                  capture_output=True, text=True, timeout=2)

            if result.returncode == 0 and 'dynamic' in result.stdout.lower():
                return f"جهاز-{ip.split('.')[-1]} (متصل)"
        except:
            pass

        # إذا فشلت جميع المحاولات، استخدم اسم افتراضي
        return f"جهاز-{ip.split('.')[-1]}"

    def get_mac_address(self, ip):
        """الحصول على MAC Address"""
        try:
            result = subprocess.run(['arp', '-a', ip],
                                  capture_output=True, text=True, timeout=2)

            if result.returncode == 0:
                # البحث عن MAC في النتيجة
                import re
                mac_pattern = r'([0-9a-fA-F]{2}[:-]){5}([0-9a-fA-F]{2})'
                match = re.search(mac_pattern, result.stdout)
                if match:
                    return match.group(0)
        except:
            pass

        return "غير معروف"

    def get_device_type(self, mac_address):
        """تحديد نوع الجهاز بناءً على MAC Address"""
        if mac_address == "غير معروف":
            return ""

        # قاعدة بيانات بسيطة لبعض الشركات المعروفة
        vendors = {
            '00:50:56': 'VMware Virtual',
            '08:00:27': 'VirtualBox',
            '00:0C:29': 'VMware',
            '00:1B:21': 'Intel NIC',
            '00:23:24': 'Apple Device',
            '28:CF:E9': 'Apple Device',
            '3C:07:54': 'Apple Device',
            'B8:27:EB': 'Raspberry Pi',
            'DC:A6:32': 'Raspberry Pi',
            '00:E0:4C': 'Realtek',
            '2C:F0:5D': 'Apple Device',
            '00:26:BB': 'Apple Device',
            'AC:DE:48': 'Apple Device',
            '00:50:B6': 'Matrix',
            '00:15:5D': 'Microsoft Hyper-V',
            '00:03:FF': 'Microsoft',
            '00:12:3F': 'Samsung',
            '28:6E:D4': 'Samsung',
            '00:16:6F': 'Intel',
            '00:1F:3C': 'Compex',
            '00:90:4C': 'Epigram',
        }

        mac_prefix = mac_address[:8].upper()
        return vendors.get(mac_prefix, "")
    
    def scan_network(self):
        """فحص الشبكة"""
        self.status_var.set("جاري فحص الشبكة...")

        def run_scan():
            try:
                # مسح الجدول
                self.root.after(0, lambda: [self.devices_tree.delete(item) for item in self.devices_tree.get_children()])

                local_ip = self.get_local_ip()
                network_base = '.'.join(local_ip.split('.')[:-1]) + '.'

                devices_found = 0

                # فحص النطاق 1-254
                for i in range(1, 255):
                    ip = network_base + str(i)

                    # تحديث شريط الحالة
                    self.root.after(0, lambda i=i: self.status_var.set(f"جاري فحص {i}/254..."))

                    if self.ping_host(ip):
                        devices_found += 1

                        # الحصول على MAC Address أولاً
                        mac_address = self.get_mac_address(ip)

                        # تحديد حالة الجهاز
                        if ip == local_ip:
                            status = "جهازك"
                            device_name = "💻 جهازك الحالي"
                        elif ip in self.blocked_devices:
                            status = "🚫 محظور"
                            device_name = self.get_device_name(ip)
                        else:
                            status = "متصل"
                            device_name = self.get_device_name(ip)

                            # إضافة نوع الجهاز إلى الاسم
                            device_type = self.get_device_type(mac_address)
                            if device_type:
                                if device_name.startswith("جهاز-"):
                                    device_name = f"📱 {device_type}"
                                else:
                                    device_name = f"📱 {device_name} ({device_type})"
                            elif not device_name.startswith("📱"):
                                device_name = f"🖥️ {device_name}"

                        # إضافة الجهاز إلى الجدول
                        self.root.after(0, lambda ip=ip, name=device_name, mac=mac_address, status=status:
                                      self.devices_tree.insert("", tk.END, text=name,
                                                             values=(ip, mac, status)))

                self.root.after(0, lambda: self.status_var.set(f"تم العثور على {devices_found} جهاز متصل"))

            except Exception as e:
                self.root.after(0, lambda: self.status_var.set(f"خطأ في الفحص: {e}"))

        scan_thread = threading.Thread(target=run_scan)
        scan_thread.daemon = True
        scan_thread.start()
    
    def test_ping(self):
        """اختبار ping لجوجل"""
        self.status_var.set("جاري اختبار الاتصال...")
        
        def run_ping():
            try:
                result = subprocess.run(['ping', '-n', '4', '*******'], 
                                      capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    self.root.after(0, lambda: self.status_var.set("الاتصال بالإنترنت يعمل بشكل طبيعي"))
                    self.root.after(0, lambda: messagebox.showinfo("نتيجة الاختبار", "الاتصال بالإنترنت يعمل بشكل طبيعي"))
                else:
                    self.root.after(0, lambda: self.status_var.set("مشكلة في الاتصال بالإنترنت"))
                    self.root.after(0, lambda: messagebox.showwarning("نتيجة الاختبار", "مشكلة في الاتصال بالإنترنت"))
                    
            except Exception as e:
                self.root.after(0, lambda: self.status_var.set(f"خطأ في الاختبار: {e}"))
        
        ping_thread = threading.Thread(target=run_ping)
        ping_thread.daemon = True
        ping_thread.start()
    
    def show_network_info(self):
        """عرض معلومات الشبكة"""
        try:
            local_ip = self.get_local_ip()
            
            # الحصول على معلومات الشبكة
            info = f"IP المحلي: {local_ip}\n"
            info += f"نطاق الشبكة: {'.'.join(local_ip.split('.')[:-1])}.0/24\n"
            
            # معلومات إضافية من psutil
            try:
                stats = psutil.net_if_stats()
                addrs = psutil.net_if_addrs()
                
                info += "\nواجهات الشبكة:\n"
                for interface, addresses in addrs.items():
                    if interface in stats and stats[interface].isup:
                        for addr in addresses:
                            if addr.family == 2:  # IPv4
                                info += f"  {interface}: {addr.address}\n"
                                break
            except:
                pass
            
            messagebox.showinfo("معلومات الشبكة", info)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في الحصول على معلومات الشبكة: {e}")
    
    def start_monitoring(self):
        """بدء مراقبة البيانات"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.status_var.set("بدأت مراقبة البيانات")
        
        def monitor():
            try:
                while self.monitoring:
                    # الحصول على إحصائيات الشبكة
                    stats = psutil.net_io_counters()
                    
                    timestamp = time.strftime("%H:%M:%S")
                    info = f"[{timestamp}] "
                    info += f"تحميل: {stats.bytes_recv / 1024 / 1024:.2f} MB | "
                    info += f"رفع: {stats.bytes_sent / 1024 / 1024:.2f} MB\n"
                    
                    self.root.after(0, lambda: self.bandwidth_text.insert(tk.END, info))
                    self.root.after(0, lambda: self.bandwidth_text.see(tk.END))
                    
                    time.sleep(2)
                    
            except Exception as e:
                self.root.after(0, lambda: self.status_var.set(f"خطأ في المراقبة: {e}"))
        
        monitor_thread = threading.Thread(target=monitor)
        monitor_thread.daemon = True
        monitor_thread.start()
    
    def stop_monitoring(self):
        """إيقاف مراقبة البيانات"""
        self.monitoring = False
        self.status_var.set("تم إيقاف مراقبة البيانات")
    
    def load_blocked_devices(self):
        """تحميل قائمة الأجهزة المحظورة"""
        try:
            import json
            import os

            if os.path.exists("blocked_devices.json"):
                with open("blocked_devices.json", "r", encoding="utf-8") as f:
                    data = json.load(f)
                    for device in data:
                        self.blocked_devices.add(device["ip"])
        except Exception as e:
            print(f"خطأ في تحميل الأجهزة المحظورة: {e}")

    def save_blocked_devices(self):
        """حفظ قائمة الأجهزة المحظورة"""
        try:
            import json
            import time

            data = []
            for ip in self.blocked_devices:
                device_name = self.get_device_name(ip)
                data.append({
                    "ip": ip,
                    "name": device_name,
                    "blocked_time": time.strftime("%Y-%m-%d %H:%M:%S")
                })

            with open("blocked_devices.json", "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"خطأ في حفظ الأجهزة المحظورة: {e}")

    def block_device_firewall(self, ip):
        """حظر جهاز باستخدام Windows Firewall"""
        try:
            rule_name = f"NetworkMonitor_Block_{ip.replace('.', '_')}"

            # حذف القواعد القديمة إن وجدت
            self.unblock_device_firewall(ip)

            success_count = 0

            # حظر الاتصالات الواردة - جميع البروتوكولات
            cmd_in_tcp = [
                'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                f'name={rule_name}_IN_TCP',
                'dir=in',
                'action=block',
                'protocol=TCP',
                f'remoteip={ip}',
                'enable=yes'
            ]

            cmd_in_udp = [
                'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                f'name={rule_name}_IN_UDP',
                'dir=in',
                'action=block',
                'protocol=UDP',
                f'remoteip={ip}',
                'enable=yes'
            ]

            # حظر الاتصالات الصادرة - جميع البروتوكولات
            cmd_out_tcp = [
                'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                f'name={rule_name}_OUT_TCP',
                'dir=out',
                'action=block',
                'protocol=TCP',
                f'remoteip={ip}',
                'enable=yes'
            ]

            cmd_out_udp = [
                'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                f'name={rule_name}_OUT_UDP',
                'dir=out',
                'action=block',
                'protocol=UDP',
                f'remoteip={ip}',
                'enable=yes'
            ]

            # تنفيذ جميع الأوامر
            commands = [cmd_in_tcp, cmd_in_udp, cmd_out_tcp, cmd_out_udp]

            for cmd in commands:
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    success_count += 1
                else:
                    print(f"فشل في تنفيذ: {' '.join(cmd)}")
                    print(f"خطأ: {result.stderr}")

            # إضافة قاعدة إضافية لحظر جميع البروتوكولات
            cmd_all = [
                'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                f'name={rule_name}_ALL',
                'dir=out',
                'action=block',
                f'remoteip={ip}',
                'enable=yes'
            ]

            result_all = subprocess.run(cmd_all, capture_output=True, text=True)
            if result_all.returncode == 0:
                success_count += 1

            print(f"تم إنشاء {success_count} قاعدة حظر للجهاز {ip}")
            return success_count >= 3  # نحتاج على الأقل 3 قواعد ناجحة

        except Exception as e:
            print(f"خطأ في حظر الجهاز: {e}")
            return False

    def unblock_device_firewall(self, ip):
        """إلغاء حظر جهاز من Windows Firewall"""
        try:
            rule_name = f"NetworkMonitor_Block_{ip.replace('.', '_')}"

            # قائمة جميع القواعد المحتملة
            rule_suffixes = ['_IN_TCP', '_IN_UDP', '_OUT_TCP', '_OUT_UDP', '_ALL']

            success_count = 0

            for suffix in rule_suffixes:
                cmd = [
                    'netsh', 'advfirewall', 'firewall', 'delete', 'rule',
                    f'name={rule_name}{suffix}'
                ]

                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    success_count += 1
                    print(f"تم حذف القاعدة: {rule_name}{suffix}")

            # محاولة حذف القواعد القديمة أيضاً
            old_rule_name = f"Block_Device_{ip.replace('.', '_')}"
            old_suffixes = ['_IN', '_OUT']

            for suffix in old_suffixes:
                cmd = [
                    'netsh', 'advfirewall', 'firewall', 'delete', 'rule',
                    f'name={old_rule_name}{suffix}'
                ]
                subprocess.run(cmd, capture_output=True)

            print(f"تم حذف {success_count} قاعدة حظر للجهاز {ip}")
            return True

        except Exception as e:
            print(f"خطأ في إلغاء حظر الجهاز: {e}")
            return False

    def block_device_arp(self, ip):
        """حظر جهاز باستخدام ARP spoofing (طريقة بديلة)"""
        try:
            # الحصول على gateway IP
            gateway_ip = self.get_gateway_ip()
            if not gateway_ip:
                return False

            # الحصول على MAC address للجهاز المستهدف
            target_mac = self.get_mac_address(ip)
            if not target_mac:
                return False

            # إضافة ARP entry ثابت خاطئ
            cmd = [
                'arp', '-s', ip, '00-00-00-00-00-00'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0

        except Exception as e:
            print(f"خطأ في ARP blocking: {e}")
            return False

    def get_gateway_ip(self):
        """الحصول على IP الـ Gateway"""
        try:
            result = subprocess.run(['ipconfig'], capture_output=True, text=True)
            lines = result.stdout.split('\n')

            for line in lines:
                if 'Default Gateway' in line or 'البوابة الافتراضية' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        gateway = parts[1].strip()
                        if gateway and gateway != '':
                            return gateway
            return None
        except:
            return None

    def block_device(self):
        """حظر جهاز"""
        ip = self.block_ip_var.get().strip()
        if not ip:
            messagebox.showwarning("تحذير", "يرجى إدخال IP Address")
            return

        if ip in self.blocked_devices:
            messagebox.showinfo("معلومات", f"الجهاز {ip} محظور بالفعل")
            return

        # التحقق من أن المستخدم لا يحظر جهازه
        local_ip = self.get_local_ip()
        if ip == local_ip:
            response = messagebox.askyesno("تحذير", "هذا هو جهازك! هل تريد حظر نفسك؟\n(قد تفقد الاتصال)")
            if not response:
                return

        response = messagebox.askyesno("تأكيد", f"هل تريد حظر الجهاز {ip}؟\nسيتم منعه من الوصول للإنترنت")
        if response:
            self.status_var.set(f"جاري حظر الجهاز {ip}...")

            def run_block():
                try:
                    # جرب الحظر بـ Firewall أولاً
                    success = self.block_device_firewall(ip)

                    # إذا فشل، جرب طريقة ARP
                    if not success:
                        print("فشل Firewall، جاري المحاولة بـ ARP...")
                        success = self.block_device_arp(ip)

                    if success:
                        self.blocked_devices.add(ip)
                        self.save_blocked_devices()
                        self.root.after(0, lambda: self.status_var.set(f"تم حظر الجهاز {ip}"))
                        self.root.after(0, lambda: messagebox.showinfo("نجح", f"تم حظر الجهاز {ip} بنجاح\n\nملاحظة: قد يحتاج الجهاز لإعادة الاتصال لتفعيل الحظر"))
                        self.root.after(0, self.refresh_blocked_list)
                        self.root.after(0, self.refresh_devices_status)
                    else:
                        self.root.after(0, lambda: self.status_var.set(f"فشل في حظر الجهاز {ip}"))
                        self.root.after(0, lambda: messagebox.showerror("خطأ", f"فشل في حظر الجهاز {ip}\n\nالأسباب المحتملة:\n• تأكد من تشغيل البرنامج كمدير\n• تأكد من تفعيل Windows Firewall\n• تحقق من صحة IP Address"))
                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("خطأ", f"خطأ في حظر الجهاز: {e}"))

            block_thread = threading.Thread(target=run_block)
            block_thread.daemon = True
            block_thread.start()

    def unblock_device(self):
        """إلغاء حظر جهاز"""
        ip = self.block_ip_var.get().strip()
        if not ip:
            messagebox.showwarning("تحذير", "يرجى إدخال IP Address")
            return

        if ip not in self.blocked_devices:
            messagebox.showinfo("معلومات", f"الجهاز {ip} غير محظور")
            return

        response = messagebox.askyesno("تأكيد", f"هل تريد إلغاء حظر الجهاز {ip}؟")
        if response:
            self.status_var.set(f"جاري إلغاء حظر الجهاز {ip}...")

            def run_unblock():
                try:
                    success = self.unblock_device_firewall(ip)
                    if success:
                        self.blocked_devices.discard(ip)
                        self.save_blocked_devices()
                        self.root.after(0, lambda: self.status_var.set(f"تم إلغاء حظر الجهاز {ip}"))
                        self.root.after(0, lambda: messagebox.showinfo("نجح", f"تم إلغاء حظر الجهاز {ip}"))
                        self.root.after(0, self.refresh_blocked_list)
                        self.root.after(0, self.refresh_devices_status)
                    else:
                        self.root.after(0, lambda: self.status_var.set(f"فشل في إلغاء حظر الجهاز {ip}"))
                        self.root.after(0, lambda: messagebox.showerror("خطأ", f"فشل في إلغاء حظر الجهاز {ip}"))
                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("خطأ", f"خطأ في إلغاء حظر الجهاز: {e}"))

            unblock_thread = threading.Thread(target=run_unblock)
            unblock_thread.daemon = True
            unblock_thread.start()

    def refresh_blocked_list(self):
        """تحديث قائمة الأجهزة المحظورة"""
        # مسح القائمة الحالية
        for item in self.blocked_tree.get_children():
            self.blocked_tree.delete(item)

        # إضافة الأجهزة المحظورة
        try:
            import json
            import os

            if os.path.exists("blocked_devices.json"):
                with open("blocked_devices.json", "r", encoding="utf-8") as f:
                    data = json.load(f)
                    for device in data:
                        self.blocked_tree.insert("", tk.END,
                                               text="🚫 محظور",
                                               values=(device["ip"], device["name"], device["blocked_time"]))
        except Exception as e:
            print(f"خطأ في تحديث قائمة الأجهزة المحظورة: {e}")

        self.status_var.set(f"تم تحديث قائمة الأجهزة المحظورة ({len(self.blocked_devices)} جهاز)")

    def unblock_selected(self):
        """إلغاء حظر الجهاز المحدد من القائمة"""
        selection = self.blocked_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد جهاز من قائمة الأجهزة المحظورة")
            return

        item = self.blocked_tree.item(selection[0])
        ip = item['values'][0]

        # تحديث حقل IP وإلغاء الحظر
        self.block_ip_var.set(ip)
        self.unblock_device()

    def unblock_all_devices(self):
        """إلغاء حظر جميع الأجهزة"""
        if not self.blocked_devices:
            messagebox.showinfo("معلومات", "لا توجد أجهزة محظورة")
            return

        response = messagebox.askyesno("تأكيد", f"هل تريد إلغاء حظر جميع الأجهزة ({len(self.blocked_devices)} جهاز)؟")
        if response:
            self.status_var.set("جاري إلغاء حظر جميع الأجهزة...")

            def run_unblock_all():
                try:
                    success_count = 0
                    total_count = len(self.blocked_devices)
                    blocked_list = list(self.blocked_devices)  # نسخة للتكرار

                    for ip in blocked_list:
                        if self.unblock_device_firewall(ip):
                            self.blocked_devices.discard(ip)
                            success_count += 1

                    self.save_blocked_devices()

                    self.root.after(0, lambda: self.status_var.set(f"تم إلغاء حظر {success_count} من {total_count} جهاز"))
                    self.root.after(0, lambda: messagebox.showinfo("انتهى", f"تم إلغاء حظر {success_count} من {total_count} جهاز"))
                    self.root.after(0, self.refresh_blocked_list)
                    self.root.after(0, self.refresh_devices_status)

                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("خطأ", f"خطأ في إلغاء الحظر: {e}"))

            unblock_thread = threading.Thread(target=run_unblock_all)
            unblock_thread.daemon = True
            unblock_thread.start()

    def refresh_devices_status(self):
        """تحديث حالة الأجهزة في قائمة الأجهزة المتصلة"""
        # تحديث حالة الأجهزة في الجدول
        for item in self.devices_tree.get_children():
            device_data = self.devices_tree.item(item)
            ip = device_data['values'][0]

            # تحديث الحالة
            if ip in self.blocked_devices:
                new_status = "🚫 محظور"
            elif ip == self.get_local_ip():
                new_status = "جهازك"
            else:
                new_status = "متصل"

            # تحديث القيم
            current_values = list(device_data['values'])
            current_values[2] = new_status  # تحديث عمود الحالة
            self.devices_tree.item(item, values=current_values)

    def show_device_details(self, event):
        """عرض تفاصيل الجهاز المحدد"""
        selection = self.devices_tree.selection()
        if not selection:
            return

        item = self.devices_tree.item(selection[0])
        device_name = item['text']
        ip = item['values'][0]
        mac = item['values'][1]
        status = item['values'][2]

        # إنشاء نافذة تفاصيل
        details_window = tk.Toplevel(self.root)
        details_window.title(f"تفاصيل الجهاز - {device_name}")
        details_window.geometry("500x400")
        details_window.resizable(False, False)

        # إطار المحتوى
        content_frame = ttk.Frame(details_window, padding="20")
        content_frame.pack(fill=tk.BOTH, expand=True)

        # معلومات أساسية
        ttk.Label(content_frame, text="معلومات الجهاز", font=("Arial", 14, "bold")).pack(anchor=tk.W, pady=(0, 10))

        info_text = f"""
🖥️ اسم الجهاز: {device_name}
🌐 عنوان IP: {ip}
🔗 عنوان MAC: {mac}
📊 الحالة: {status}
🏷️ نوع الجهاز: {self.get_device_type(mac) if mac != "غير معروف" else "غير معروف"}
        """

        info_label = ttk.Label(content_frame, text=info_text, font=("Arial", 10))
        info_label.pack(anchor=tk.W, pady=(0, 20))

        # أزرار العمليات
        buttons_frame = ttk.Frame(content_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(buttons_frame, text="اختبار Ping",
                  command=lambda: self.ping_specific_device(ip)).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="نسخ IP",
                  command=lambda: self.copy_to_clipboard(ip)).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="نسخ MAC",
                  command=lambda: self.copy_to_clipboard(mac)).pack(side=tk.LEFT, padx=(0, 10))

        # زر الحظر/إلغاء الحظر
        if ip in self.blocked_devices:
            ttk.Button(buttons_frame, text="إلغاء الحظر",
                      command=lambda: self.quick_unblock(ip, details_window)).pack(side=tk.LEFT, padx=(0, 10))
        else:
            ttk.Button(buttons_frame, text="حظر الجهاز",
                      command=lambda: self.quick_block(ip, details_window)).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="إغلاق",
                  command=details_window.destroy).pack(side=tk.RIGHT)

        # تحديث معلومات إضافية
        self.update_device_details(content_frame, ip)

    def ping_specific_device(self, ip):
        """اختبار ping لجهاز محدد"""
        def run_ping():
            try:
                result = subprocess.run(['ping', '-n', '4', ip],
                                      capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    messagebox.showinfo("نتيجة Ping", f"الجهاز {ip} يستجيب بشكل طبيعي")
                else:
                    messagebox.showwarning("نتيجة Ping", f"الجهاز {ip} لا يستجيب")

            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في اختبار Ping: {e}")

        ping_thread = threading.Thread(target=run_ping)
        ping_thread.daemon = True
        ping_thread.start()

    def copy_to_clipboard(self, text):
        """نسخ النص إلى الحافظة"""
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        messagebox.showinfo("تم", f"تم نسخ: {text}")

    def update_device_details(self, parent_frame, ip):
        """تحديث معلومات إضافية للجهاز"""
        # إضافة معلومات إضافية
        additional_frame = ttk.LabelFrame(parent_frame, text="معلومات إضافية", padding="10")
        additional_frame.pack(fill=tk.X, pady=(20, 0))

        # معلومات الشبكة
        try:
            # محاولة الحصول على معلومات إضافية
            hostname_info = "جاري البحث..."
            ttk.Label(additional_frame, text=f"Hostname: {hostname_info}").pack(anchor=tk.W)

            # تحديث المعلومات في خيط منفصل
            def get_additional_info():
                try:
                    hostname = socket.gethostbyaddr(ip)[0]
                    additional_frame.children['!label'].config(text=f"Hostname: {hostname}")
                except:
                    additional_frame.children['!label'].config(text="Hostname: غير متوفر")

            info_thread = threading.Thread(target=get_additional_info)
            info_thread.daemon = True
            info_thread.start()

        except Exception as e:
            ttk.Label(additional_frame, text=f"خطأ في الحصول على المعلومات: {e}").pack(anchor=tk.W)

    def quick_block(self, ip, parent_window):
        """حظر سريع للجهاز من نافذة التفاصيل"""
        self.block_ip_var.set(ip)
        parent_window.destroy()
        self.block_device()

    def quick_unblock(self, ip, parent_window):
        """إلغاء حظر سريع للجهاز من نافذة التفاصيل"""
        self.block_ip_var.set(ip)
        parent_window.destroy()
        self.unblock_device()

    def check_admin_privileges(self):
        """التحقق من صلاحيات المدير"""
        try:
            result = subprocess.run(['net', 'session'], capture_output=True, text=True)
            return result.returncode == 0
        except:
            return False

    def show_admin_warning(self):
        """عرض تحذير عدم وجود صلاحيات مدير"""
        warning_msg = """⚠️ تحذير: البرنامج لا يعمل بصلاحيات المدير!

🔒 وظائف الحظر تتطلب صلاحيات المدير للعمل بشكل صحيح.

💡 للحصول على صلاحيات المدير:
• أغلق البرنامج
• انقر مرتين على run_as_admin.bat
• أو انقر بالزر الأيمن على البرنامج واختر "Run as administrator"

هل تريد المتابعة بدون صلاحيات المدير؟
(ستعمل وظائف المراقبة فقط)"""

        response = messagebox.askyesno("تحذير صلاحيات المدير", warning_msg)
        return response

    def run(self):
        """تشغيل التطبيق"""
        # التحقق من صلاحيات المدير
        if not self.check_admin_privileges():
            if not self.show_admin_warning():
                self.root.destroy()
                return
            else:
                # إضافة تحذير في شريط الحالة
                self.status_var.set("⚠️ تحذير: لا توجد صلاحيات مدير - وظائف الحظر معطلة")

        # تحديث قائمة الأجهزة المحظورة عند البدء
        self.refresh_blocked_list()

        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = SimpleNetworkMonitor()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")
