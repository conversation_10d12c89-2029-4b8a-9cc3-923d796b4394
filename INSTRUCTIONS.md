# تعليمات الاستخدام - Steam Lua Injector المطور

## 🚀 الميزات الجديدة

### ✨ التبويبات الأربعة:
1. **عبة واحدة** - للعمل على عبة محددة
2. **البحث في الألعاب** - البحث في جميع ألعاب Steam
3. **الألعاب المثبتة** - عرض الألعاب المثبتة على جهازك
4. **الحقن الجماعي** - حقن كود في جميع الألعاب المثبتة

## خطوات التشغيل

### 1. التشغيل السريع
- انقر مرتين على ملف `run.bat`
- أو افتح Command Prompt وشغل `python main.py`

### 2. استخدام التبويبات

#### 📱 تبويب "عبة واحدة"
1. أ<PERSON><PERSON><PERSON> App ID للعبة المطلوبة
2. اضغ<PERSON> "تحميل العبة"
3. حدد ملف Lua من القائمة
4. اختر العملية المطلوبة

#### 🔍 تبويب "البحث في الألعاب"
1. اكتب اسم العبة في حقل البحث
2. اضغط "بحث" أو Enter
3. حدد العبة من النتائج
4. اضغط "تحميل العبة المحددة"
5. سيتم التبديل تلقائياً لتبويب "عبة واحدة"

#### 💻 تبويب "الألعاب المثبتة"
1. اضغط "تحديث قائمة الألعاب المثبتة"
2. حدد العبة المطلوبة من القائمة
3. اضغط "تحميل العبة المحددة" أو "فتح مجلد العبة"

#### ⚡ تبويب "الحقن الجماعي"
1. اكتب كود Lua في المنطقة النصية
2. اختر موضع الحقن (بداية أو نهاية)
3. اضغط "بدء الحقن الجماعي"
4. تابع النتائج في منطقة النتائج

### 3. العثور على App ID (طرق متعددة)

#### أ. من البرنامج نفسه (الأسهل!)
- استخدم تبويب "البحث في الألعاب"
- ابحث عن اسم العبة
- انسخ App ID من النتائج

#### ب. من Steam Store
- انظر إلى رابط العبة `store.steampowered.com/app/XXXXXX`

#### ج. من SteamDB
- ابحث عن العبة في steamdb.info

#### د. أمثلة على App IDs شائعة
- Counter-Strike 2: `730`
- Dota 2: `570`
- Team Fortress 2: `440`
- Left 4 Dead 2: `550`
- Portal 2: `620`

### 3. العمليات المتاحة

#### حقن الكود
1. حدد ملف Lua من القائمة
2. اكتب الكود المراد حقنه في المنطقة النصية
3. اختر الموضع (بداية أو نهاية الملف)
4. اضغط "حقن الكود"

#### استبدال الملف
1. حدد ملف Lua من القائمة
2. اضغط "استبدال بملف"
3. اختر الملف الجديد من جهازك

#### النسخ الاحتياطية
- **إنشاء نسخة احتياطية**: حدد الملف واضغط "نسخ احتياطية"
- **استعادة النسخة**: حدد الملف واضغط "استعادة النسخة الاحتياطية"

## نصائح مهمة

### قبل البدء
- ✅ أغلق العبة تماماً قبل التعديل
- ✅ أنشئ نسخة احتياطية من مجلد العبة كاملاً
- ✅ تأكد من أن Steam يعمل

### أثناء الاستخدام
- 🔍 استخدم "عرض الملف" لفهم محتوى ملف Lua قبل التعديل
- 💾 أنشئ نسخة احتياطية قبل كل تعديل
- 🧪 اختبر التعديلات على ملف واحد أولاً

### بعد التعديل
- 🎮 شغل العبة واختبر التغييرات
- 🔄 إذا حدثت مشاكل، استعد النسخة الاحتياطية فوراً
- 📝 احتفظ بسجل للتعديلات التي قمت بها

## أمثلة عملية

### مثال 1: إضافة رسالة ترحيب
```lua
print("مرحباً! تم تحميل التعديل بنجاح")
```

### مثال 2: تعديل متغير
```lua
-- تغيير قيمة متغير موجود
if player_health then
    player_health = player_health * 2  -- مضاعفة الصحة
end
```

### مثال 3: إضافة دالة جديدة
```lua
function custom_cheat()
    print("تم تفعيل الغش المخصص")
    -- كود الغش هنا
end
```

## استكشاف الأخطاء

### المشاكل الشائعة

#### "لم يتم العثور على العبة"
- تأكد من صحة App ID
- تأكد من تثبيت العبة
- تأكد من تشغيل Steam

#### "فشل في حقن الكود"
- تأكد من إغلاق العبة
- تأكد من صلاحيات الكتابة
- جرب تشغيل البرنامج كمدير

#### "لم يتم العثور على ملفات Lua"
- بعض الألعاب لا تستخدم Lua
- قد تكون الملفات في مجلدات فرعية
- جرب البحث يدوياً في مجلد العبة

### الحلول
1. **تشغيل كمدير**: انقر بالزر الأيمن على `run.bat` واختر "تشغيل كمدير"
2. **إعادة تثبيت المتطلبات**: `pip install --upgrade -r requirements.txt`
3. **التحقق من مسار Steam**: تأكد من تثبيت Steam في المسار الصحيح

## تحذيرات الأمان

⚠️ **مهم جداً**
- هذا البرنامج للاستخدام الشخصي فقط
- لا تستخدمه في الألعاب متعددة اللاعبين
- قد يؤدي إلى حظر من Steam إذا استخدم بشكل خاطئ
- احتفظ دائماً بنسخ احتياطية

## الدعم الفني

إذا واجهت مشاكل:
1. تأكد من اتباع التعليمات بدقة
2. تحقق من ملف `README.md` للمزيد من المعلومات
3. جرب إعادة تشغيل البرنامج
4. تأكد من تحديث Python والمكتبات
