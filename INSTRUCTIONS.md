# تعليمات الاستخدام - Steam Lua Injector

## خطوات التشغيل

### 1. التشغيل السريع
- انقر مرتين على ملف `run.bat`
- أو افتح Command Prompt وشغل `python main.py`

### 2. استخدام البرنامج

#### أ. تحميل العبة
1. ابحث عن App ID للعبة المطلوبة
2. أدخل App ID في الحقل المخصص
3. اضغط "تحميل العبة"

#### ب. العثور على App ID
- **من Steam Store**: انظر إلى رابط العبة `store.steampowered.com/app/XXXXXX`
- **من SteamDB**: ابحث عن العبة في steamdb.info
- **من مجلد Steam**: ابحث عن ملفات `appmanifest_XXXXXX.acf`

#### ج. أمثلة على App IDs شائعة
- Counter-Strike 2: `730`
- Dota 2: `570`
- Team Fortress 2: `440`
- Left 4 Dead 2: `550`
- Portal 2: `620`

### 3. العمليات المتاحة

#### حقن الكود
1. حدد ملف Lua من القائمة
2. اكتب الكود المراد حقنه في المنطقة النصية
3. اختر الموضع (بداية أو نهاية الملف)
4. اضغط "حقن الكود"

#### استبدال الملف
1. حدد ملف Lua من القائمة
2. اضغط "استبدال بملف"
3. اختر الملف الجديد من جهازك

#### النسخ الاحتياطية
- **إنشاء نسخة احتياطية**: حدد الملف واضغط "نسخ احتياطية"
- **استعادة النسخة**: حدد الملف واضغط "استعادة النسخة الاحتياطية"

## نصائح مهمة

### قبل البدء
- ✅ أغلق العبة تماماً قبل التعديل
- ✅ أنشئ نسخة احتياطية من مجلد العبة كاملاً
- ✅ تأكد من أن Steam يعمل

### أثناء الاستخدام
- 🔍 استخدم "عرض الملف" لفهم محتوى ملف Lua قبل التعديل
- 💾 أنشئ نسخة احتياطية قبل كل تعديل
- 🧪 اختبر التعديلات على ملف واحد أولاً

### بعد التعديل
- 🎮 شغل العبة واختبر التغييرات
- 🔄 إذا حدثت مشاكل، استعد النسخة الاحتياطية فوراً
- 📝 احتفظ بسجل للتعديلات التي قمت بها

## أمثلة عملية

### مثال 1: إضافة رسالة ترحيب
```lua
print("مرحباً! تم تحميل التعديل بنجاح")
```

### مثال 2: تعديل متغير
```lua
-- تغيير قيمة متغير موجود
if player_health then
    player_health = player_health * 2  -- مضاعفة الصحة
end
```

### مثال 3: إضافة دالة جديدة
```lua
function custom_cheat()
    print("تم تفعيل الغش المخصص")
    -- كود الغش هنا
end
```

## استكشاف الأخطاء

### المشاكل الشائعة

#### "لم يتم العثور على العبة"
- تأكد من صحة App ID
- تأكد من تثبيت العبة
- تأكد من تشغيل Steam

#### "فشل في حقن الكود"
- تأكد من إغلاق العبة
- تأكد من صلاحيات الكتابة
- جرب تشغيل البرنامج كمدير

#### "لم يتم العثور على ملفات Lua"
- بعض الألعاب لا تستخدم Lua
- قد تكون الملفات في مجلدات فرعية
- جرب البحث يدوياً في مجلد العبة

### الحلول
1. **تشغيل كمدير**: انقر بالزر الأيمن على `run.bat` واختر "تشغيل كمدير"
2. **إعادة تثبيت المتطلبات**: `pip install --upgrade -r requirements.txt`
3. **التحقق من مسار Steam**: تأكد من تثبيت Steam في المسار الصحيح

## تحذيرات الأمان

⚠️ **مهم جداً**
- هذا البرنامج للاستخدام الشخصي فقط
- لا تستخدمه في الألعاب متعددة اللاعبين
- قد يؤدي إلى حظر من Steam إذا استخدم بشكل خاطئ
- احتفظ دائماً بنسخ احتياطية

## الدعم الفني

إذا واجهت مشاكل:
1. تأكد من اتباع التعليمات بدقة
2. تحقق من ملف `README.md` للمزيد من المعلومات
3. جرب إعادة تشغيل البرنامج
4. تأكد من تحديث Python والمكتبات
