#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أداة اختبار الحظر - للتحقق من فعالية حظر الأجهزة
"""

import subprocess
import sys
import time

def test_firewall_rules():
    """اختبار قواعد Windows Firewall"""
    print("🔍 فحص قواعد Windows Firewall...")
    
    try:
        # عرض جميع قواعد الحظر المنشأة بواسطة البرنامج
        cmd = ['netsh', 'advfirewall', 'firewall', 'show', 'rule', 'name=all']
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            rules = result.stdout
            network_monitor_rules = []
            
            for line in rules.split('\n'):
                if 'NetworkMonitor_Block' in line:
                    network_monitor_rules.append(line.strip())
            
            if network_monitor_rules:
                print(f"✅ تم العثور على {len(network_monitor_rules)} قاعدة حظر:")
                for rule in network_monitor_rules[:10]:  # عرض أول 10 قواعد
                    print(f"   • {rule}")
                if len(network_monitor_rules) > 10:
                    print(f"   ... و {len(network_monitor_rules) - 10} قاعدة أخرى")
            else:
                print("❌ لم يتم العثور على قواعد حظر")
                
        else:
            print(f"❌ خطأ في فحص القواعد: {result.stderr}")
            
    except Exception as e:
        print(f"❌ خطأ في الفحص: {e}")

def test_ping(ip):
    """اختبار ping للجهاز"""
    print(f"🏓 اختبار ping للجهاز {ip}...")
    
    try:
        cmd = ['ping', '-n', '4', ip]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ الجهاز {ip} يستجيب للـ ping")
            
            # تحليل النتائج
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Reply from' in line or 'رد من' in line:
                    print(f"   📡 {line.strip()}")
        else:
            print(f"❌ الجهاز {ip} لا يستجيب للـ ping")
            
    except Exception as e:
        print(f"❌ خطأ في ping: {e}")

def test_internet_access(ip):
    """اختبار وصول الجهاز للإنترنت (محاكاة)"""
    print(f"🌐 اختبار وصول الجهاز {ip} للإنترنت...")
    
    # هذا اختبار تقريبي - في الواقع نحتاج للوصول للجهاز نفسه
    print("ℹ️  لاختبار الوصول للإنترنت، جرب من الجهاز المحظور:")
    print("   • افتح متصفح الإنترنت")
    print("   • جرب زيارة google.com")
    print("   • إذا لم يفتح الموقع = الحظر يعمل ✅")
    print("   • إذا فتح الموقع = الحظر لا يعمل ❌")

def check_admin_privileges():
    """التحقق من صلاحيات المدير"""
    print("🔐 فحص صلاحيات المدير...")
    
    try:
        # محاولة تنفيذ أمر يتطلب صلاحيات مدير
        result = subprocess.run(['net', 'session'], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ البرنامج يعمل بصلاحيات المدير")
            return True
        else:
            print("❌ البرنامج لا يعمل بصلاحيات المدير")
            print("💡 استخدم run_as_admin.bat لتشغيل البرنامج بصلاحيات المدير")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص الصلاحيات: {e}")
        return False

def check_firewall_status():
    """فحص حالة Windows Firewall"""
    print("🛡️ فحص حالة Windows Firewall...")
    
    try:
        cmd = ['netsh', 'advfirewall', 'show', 'allprofiles', 'state']
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            output = result.stdout
            if 'ON' in output.upper() or 'تشغيل' in output:
                print("✅ Windows Firewall مفعل")
                return True
            else:
                print("❌ Windows Firewall معطل")
                print("💡 فعل Windows Firewall من لوحة التحكم")
                return False
        else:
            print(f"❌ خطأ في فحص Firewall: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص Firewall: {e}")
        return False

def main():
    """الوظيفة الرئيسية"""
    print("=" * 50)
    print("🧪 أداة اختبار حظر الأجهزة")
    print("=" * 50)
    print()
    
    # فحص الصلاحيات
    has_admin = check_admin_privileges()
    print()
    
    # فحص Firewall
    firewall_ok = check_firewall_status()
    print()
    
    # فحص القواعد
    test_firewall_rules()
    print()
    
    # اختبار جهاز محدد
    if len(sys.argv) > 1:
        test_ip = sys.argv[1]
        print(f"🎯 اختبار الجهاز المحدد: {test_ip}")
        print("-" * 30)
        
        test_ping(test_ip)
        print()
        
        test_internet_access(test_ip)
        print()
    else:
        print("💡 لاختبار جهاز محدد، استخدم:")
        print(f"   python {sys.argv[0]} *************")
        print()
    
    # ملخص النتائج
    print("📋 ملخص النتائج:")
    print(f"   • صلاحيات المدير: {'✅' if has_admin else '❌'}")
    print(f"   • Windows Firewall: {'✅' if firewall_ok else '❌'}")
    
    if has_admin and firewall_ok:
        print("\n🎉 النظام جاهز لحظر الأجهزة!")
    else:
        print("\n⚠️ يجب إصلاح المشاكل أعلاه أولاً")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
