@echo off
echo ========================================
echo       مراقب ومدير الشبكة
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)

REM تثبيت المتطلبات
echo جاري التحقق من المتطلبات...
pip install -r requirements.txt

REM التحقق من نجاح التثبيت
if errorlevel 1 (
    echo تحذير: قد تكون بعض المكتبات غير مثبتة بشكل صحيح
    echo.
)

REM تشغيل البرنامج
echo جاري تشغيل مراقب الشبكة...
echo.
python main.py

pause
