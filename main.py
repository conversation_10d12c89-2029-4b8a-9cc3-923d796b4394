"""
Steam Lua Injector - الملف الرئيسي
برنامج لحقن وتعديل ملفات Lua في ألعاب Steam

الاستخدام:
python main.py

المطور: للاستخدام الشخصي فقط
"""
import sys
import os
import tkinter as tk
from tkinter import messagebox

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from gui import SteamInjectorGUI
    from steam_injector import SteamInjector
    from config import Config
except ImportError as e:
    print(f"خطأ في استيراد المكتبات: {e}")
    print("تأكد من تثبيت جميع المتطلبات باستخدام: pip install -r requirements.txt")
    sys.exit(1)

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    try:
        import psutil
        import winreg
        return True
    except ImportError as e:
        messagebox.showerror("خطأ", f"مكتبة مفقودة: {e}\nيرجى تثبيت المتطلبات باستخدام:\npip install -r requirements.txt")
        return False

def main():
    """الوظيفة الرئيسية"""
    print("Steam Lua Injector")
    print("==================")
    print("للاستخدام الشخصي فقط")
    print()
    
    # التحقق من المتطلبات
    if not check_requirements():
        return
    
    # التحقق من وجود Steam
    config = Config()
    if not config.steam_path:
        response = messagebox.askyesno(
            "تحذير", 
            "لم يتم العثور على Steam في المسارات الافتراضية.\nهل تريد المتابعة؟"
        )
        if not response:
            return
    else:
        print(f"تم العثور على Steam في: {config.steam_path}")
    
    # تشغيل الواجهة الرسومية
    try:
        app = SteamInjectorGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("خطأ", f"خطأ في تشغيل البرنامج: {e}")
        print(f"خطأ: {e}")

if __name__ == "__main__":
    main()
