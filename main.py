"""
مراقب ومدير الشبكة - الملف الرئيسي
برنامج شامل لمراقبة وإدارة الشبكة المحلية

الميزات:
- فحص الأجهزة المتصلة بالشبكة
- مراقبة استهلاك البيانات
- اختبار سرعة الشبكة والإنترنت
- حظر وإدارة الأجهزة

المطور: للاستخدام الشخصي وإدارة الشبكات
"""
import sys
import os
import tkinter as tk
from tkinter import messagebox
import ctypes

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_admin_privileges():
    """التحقق من صلاحيات المدير"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """تشغيل البرنامج بصلاحيات المدير"""
    try:
        if sys.argv[-1] != 'asadmin':
            script = os.path.abspath(sys.argv[0])
            params = ' '.join([script] + sys.argv[1:] + ['asadmin'])
            ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, params, None, 1)
            return True
        return False
    except:
        return False

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    missing_modules = []
    
    try:
        import psutil
    except ImportError:
        missing_modules.append("psutil")
    
    try:
        import netifaces
    except ImportError:
        missing_modules.append("netifaces")
    
    try:
        import speedtest
    except ImportError:
        missing_modules.append("speedtest-cli")
    
    try:
        import netaddr
    except ImportError:
        missing_modules.append("netaddr")
    
    if missing_modules:
        error_msg = "المكتبات التالية مفقودة:\n"
        error_msg += "\n".join(f"- {module}" for module in missing_modules)
        error_msg += "\n\nيرجى تثبيتها باستخدام:\n"
        error_msg += f"pip install {' '.join(missing_modules)}"
        
        messagebox.showerror("مكتبات مفقودة", error_msg)
        return False
    
    return True

def show_welcome_message():
    """عرض رسالة ترحيب"""
    welcome_msg = """
🌐 مراقب ومدير الشبكة
========================

الميزات الرئيسية:
✅ فحص الأجهزة المتصلة بالشبكة
✅ مراقبة استهلاك البيانات في الوقت الفعلي
✅ اختبار سرعة الإنترنت والشبكة المحلية
✅ حظر وإدارة الأجهزة على الشبكة

⚠️ تحذيرات مهمة:
- يتطلب صلاحيات المدير لبعض الوظائف
- للاستخدام على الشبكة المحلية فقط
- تأكد من أنك مالك الشبكة أو لديك إذن

هل تريد المتابعة؟
    """
    
    response = messagebox.askyesno("مرحباً", welcome_msg)
    return response

def main():
    """الوظيفة الرئيسية"""
    print("🌐 مراقب ومدير الشبكة")
    print("=" * 30)
    print("للاستخدام الشخصي وإدارة الشبكات")
    print()
    
    # التحقق من المتطلبات
    if not check_requirements():
        input("اضغط Enter للخروج...")
        return
    
    # عرض رسالة الترحيب
    if not show_welcome_message():
        return
    
    # التحقق من صلاحيات المدير
    if not check_admin_privileges():
        response = messagebox.askyesno(
            "صلاحيات المدير", 
            "بعض الوظائف تتطلب صلاحيات المدير (مثل حظر الأجهزة).\n"
            "هل تريد إعادة تشغيل البرنامج بصلاحيات المدير؟"
        )
        
        if response:
            if run_as_admin():
                return
            else:
                messagebox.showwarning(
                    "تحذير", 
                    "فشل في الحصول على صلاحيات المدير.\n"
                    "ستعمل بعض الوظائف بشكل محدود."
                )
    
    # تشغيل الواجهة الرسومية
    try:
        from gui import NetworkMonitorGUI
        
        print("جاري تشغيل الواجهة الرسومية...")
        app = NetworkMonitorGUI()
        app.run()
        
    except ImportError as e:
        messagebox.showerror("خطأ", f"خطأ في استيراد الواجهة: {e}")
        print(f"خطأ في استيراد الواجهة: {e}")
    except Exception as e:
        messagebox.showerror("خطأ", f"خطأ في تشغيل البرنامج: {e}")
        print(f"خطأ: {e}")

if __name__ == "__main__":
    main()
