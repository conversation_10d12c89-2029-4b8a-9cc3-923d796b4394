#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ST Injector VIP 👑
Steam Game Downloader with VIP Interface
مطابق لتصميم ST Injector VIP الأصلي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import subprocess
import threading
import json
import time
from datetime import datetime

class STInjectorVIP:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("ST Injector VIP 👑")
        self.root.geometry("800x600")
        self.root.configure(bg='#1a1a1a')
        self.root.resizable(False, False)
        
        # متغيرات التطبيق
        self.app_id = tk.StringVar()
        self.download_path = tk.StringVar(value="F:\\Steam")
        self.username = tk.StringVar()
        self.password = tk.StringVar()
        
        # حالة التحميل
        self.is_downloading = False
        self.download_process = None
        self.progress_var = tk.IntVar()
        
        # إنشاء الواجهة
        self.create_interface()
    
    def create_interface(self):
        """إنشاء واجهة مطابقة لـ ST Injector VIP"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#1a1a1a')
        main_frame.pack(fill='both', expand=True)
        
        # الشريط الجانبي الأيسر (مثل ST Injector)
        self.create_sidebar(main_frame)
        
        # المنطقة الرئيسية
        self.create_main_area(main_frame)
    
    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي مثل ST Injector VIP"""
        sidebar = tk.Frame(parent, bg='#2d2d2d', width=200)
        sidebar.pack(side='left', fill='y')
        sidebar.pack_propagate(False)
        
        # العنوان
        title_label = tk.Label(sidebar, text="ST Injector VIP 👑", 
                              font=('Arial', 14, 'bold'), fg='#ffd700', bg='#2d2d2d')
        title_label.pack(pady=20)
        
        # أزرار التنقل
        nav_buttons = [
            ("🏠 Home", self.show_home),
            ("🎮 Games", self.show_games),
        ]
        
        for text, command in nav_buttons:
            btn = tk.Button(sidebar, text=text, command=command,
                           bg='#3d3d3d', fg='white', font=('Arial', 11),
                           relief='flat', padx=20, pady=10, width=15)
            btn.pack(pady=5, padx=10, fill='x')
        
        # مساحة فارغة
        tk.Frame(sidebar, bg='#2d2d2d', height=50).pack()
        
        # أزرار إضافية (مثل الصورة)
        extra_buttons = [
            ("Version 2.3", None),
            ("Join Discord", self.open_discord),
            ("Join Telegram", self.open_telegram)
        ]
        
        for text, command in extra_buttons:
            if command:
                btn = tk.Button(sidebar, text=text, command=command,
                               bg='#4a90e2', fg='white', font=('Arial', 10),
                               relief='flat', padx=10, pady=5)
            else:
                btn = tk.Label(sidebar, text=text, fg='#888', bg='#2d2d2d',
                              font=('Arial', 10))
            btn.pack(pady=2, padx=10)
    
    def create_main_area(self, parent):
        """إنشاء المنطقة الرئيسية مثل ST Injector VIP"""
        main_area = tk.Frame(parent, bg='#1a1a1a')
        main_area.pack(side='right', fill='both', expand=True)
        
        # العنوان الرئيسي
        title_frame = tk.Frame(main_area, bg='#1a1a1a')
        title_frame.pack(fill='x', pady=20)
        
        title_label = tk.Label(title_frame, text="Garry's Mod", 
                              font=('Arial', 24, 'bold'), fg='#ffd700', bg='#1a1a1a')
        title_label.pack()
        
        # حقل App ID (مثل الصورة)
        app_frame = tk.Frame(main_area, bg='#1a1a1a')
        app_frame.pack(pady=20)
        
        self.app_entry = tk.Entry(app_frame, textvariable=self.app_id, 
                                 font=('Arial', 16), width=25, justify='center',
                                 bg='#2d2d2d', fg='white', insertbackground='white',
                                 relief='solid', bd=2)
        self.app_entry.pack(pady=10)
        self.app_entry.insert(0, "4000")  # مثل الصورة
        
        # شريط التقدم
        progress_frame = tk.Frame(main_area, bg='#1a1a1a')
        progress_frame.pack(pady=10)
        
        tk.Label(progress_frame, text="Ready", font=('Arial', 12), 
                fg='#ffd700', bg='#1a1a1a').pack(side='left')
        
        tk.Label(progress_frame, text="0%", font=('Arial', 12), 
                fg='white', bg='#1a1a1a').pack(side='right')
        
        # أزرار العمليات
        buttons_frame = tk.Frame(main_area, bg='#1a1a1a')
        buttons_frame.pack(pady=20)
        
        # الصف الأول من الأزرار
        row1 = tk.Frame(buttons_frame, bg='#1a1a1a')
        row1.pack(pady=5)
        
        add_btn = tk.Button(row1, text="Add game", command=self.add_game,
                           bg='#ffd700', fg='black', font=('Arial', 12, 'bold'),
                           padx=20, pady=10, relief='flat')
        add_btn.pack(side='left', padx=5)
        
        remove_btn = tk.Button(row1, text="Remove game", command=self.remove_game,
                              bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                              padx=20, pady=10, relief='flat')
        remove_btn.pack(side='left', padx=5)
        
        call_btn = tk.Button(row1, text="📞", command=self.call_function,
                            bg='#ffd700', fg='black', font=('Arial', 12, 'bold'),
                            width=3, height=1, relief='flat')
        call_btn.pack(side='left', padx=5)
        
        # نص البحث
        search_label = tk.Label(main_area, text="Can search the game ID here",
                               font=('Arial', 12), fg='white', bg='#1a1a1a')
        search_label.pack(pady=10)
        
        # زر SteamDB
        steamdb_btn = tk.Button(main_area, text="SteamDB", command=self.open_steamdb,
                               bg='#ffd700', fg='black', font=('Arial', 12, 'bold'),
                               padx=30, pady=10, relief='flat')
        steamdb_btn.pack(pady=10)
        
        # نص Enjoy
        enjoy_label = tk.Label(main_area, text="Enjoy!",
                              font=('Arial', 16, 'bold'), fg='#ffd700', bg='#1a1a1a')
        enjoy_label.pack(pady=20)
        
        # مسار التحميل
        path_label = tk.Label(main_area, text=f"Path: {self.download_path.get()}",
                             font=('Arial', 10), fg='#888', bg='#1a1a1a')
        path_label.pack(pady=5)
    
    def show_home(self):
        """عرض الصفحة الرئيسية"""
        messagebox.showinfo("Home", "الصفحة الرئيسية")
    
    def show_games(self):
        """عرض قائمة الألعاب"""
        messagebox.showinfo("Games", "قائمة الألعاب")
    
    def add_game(self):
        """إضافة لعبة (بدء التحميل)"""
        app_id = self.app_id.get().strip()
        if not app_id:
            messagebox.showerror("خطأ", "يرجى إدخال App ID")
            return
        
        # بدء التحميل
        self.start_download(app_id)
    
    def remove_game(self):
        """إزالة لعبة (إيقاف التحميل)"""
        if self.is_downloading:
            self.stop_download()
        else:
            messagebox.showinfo("معلومات", "لا يوجد تحميل جاري")
    
    def call_function(self):
        """وظيفة الاتصال"""
        messagebox.showinfo("اتصال", "وظيفة الاتصال")
    
    def open_steamdb(self):
        """فتح SteamDB للبحث"""
        app_id = self.app_id.get().strip()
        if app_id:
            url = f"https://steamdb.info/app/{app_id}/"
        else:
            url = "https://steamdb.info/"
        
        try:
            os.startfile(url)
        except:
            messagebox.showinfo("SteamDB", f"افتح الرابط: {url}")
    
    def open_discord(self):
        """فتح Discord"""
        messagebox.showinfo("Discord", "انضم إلى Discord")
    
    def open_telegram(self):
        """فتح Telegram"""
        messagebox.showinfo("Telegram", "انضم إلى Telegram")
    
    def start_download(self, app_id):
        """بدء تحميل اللعبة الحقيقي"""
        self.is_downloading = True

        # قاعدة بيانات Depots للألعاب الشائعة
        depots_database = {
            "4000": "4001",  # Garry's Mod
            "730": "731",    # CS:GO
            "440": "441",    # TF2
            "570": "571",    # Dota 2
            "271590": "271591",  # GTA V
            "292030": "292031",  # Witcher 3
            "578080": "578081",  # PUBG
            "1172470": "1172471"  # Apex Legends
        }

        depot_id = depots_database.get(app_id, f"{app_id}1")

        messagebox.showinfo("تحميل", f"بدء تحميل اللعبة {app_id}\nDepot: {depot_id}")

        def download_worker():
            try:
                # بناء أمر DepotDownloader
                depot_exe = os.path.join(os.path.dirname(__file__), "DepotDownloader.exe")

                if not os.path.exists(depot_exe):
                    messagebox.showerror("خطأ", "DepotDownloader.exe غير موجود!")
                    self.is_downloading = False
                    return

                download_dir = self.download_path.get()
                os.makedirs(download_dir, exist_ok=True)

                cmd = [depot_exe, "-app", app_id, "-depot", depot_id, "-dir", download_dir]

                # إضافة بيانات تسجيل الدخول إذا كانت متوفرة
                username = self.username.get().strip()
                password = self.password.get().strip()
                if username and password:
                    cmd.extend(["-username", username, "-password", password])

                # تشغيل DepotDownloader
                self.download_process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    cwd=os.path.dirname(__file__)
                )

                # مراقبة التقدم
                while self.download_process.poll() is None and self.is_downloading:
                    time.sleep(0.5)

                # التحقق من النتيجة
                if self.is_downloading:
                    return_code = self.download_process.wait()
                    if return_code == 0:
                        messagebox.showinfo("اكتمل", f"تم تحميل اللعبة {app_id} بنجاح!")
                    else:
                        error_output = self.download_process.stderr.read()
                        messagebox.showerror("خطأ", f"فشل التحميل:\n{error_output}")
                else:
                    self.download_process.terminate()
                    messagebox.showinfo("إيقاف", "تم إيقاف التحميل")

                self.is_downloading = False

            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في التحميل: {str(e)}")
                self.is_downloading = False

        threading.Thread(target=download_worker, daemon=True).start()
    
    def stop_download(self):
        """إيقاف التحميل"""
        self.is_downloading = False
        messagebox.showinfo("إيقاف", "تم إيقاف التحميل")
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = STInjectorVIP()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")
