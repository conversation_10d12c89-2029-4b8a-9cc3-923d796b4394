#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أداة إرسال الرسائل عبر الشبكة المحلية
Network Messenger - إرسال رسائل للأجهزة عبر IP
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import socket
import threading
import json
import time
from datetime import datetime
import subprocess
import os

class NetworkMessenger:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("📱 مرسل الرسائل الشبكي - Network Messenger")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # إعدادات الشبكة
        self.DEFAULT_PORT = 9999
        self.server_socket = None
        self.is_listening = False
        
        # قائمة الرسائل
        self.messages = []
        
        # إنشاء الواجهة
        self.create_interface()
        
        # بدء الاستماع للرسائل
        self.start_listening()
    
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', padx=5, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="📱 مرسل الرسائل الشبكي", 
                              font=('Arial', 16, 'bold'), fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        # إطار المعلومات
        info_frame = tk.Frame(self.root, bg='#ecf0f1', height=40)
        info_frame.pack(fill='x', padx=5, pady=2)
        info_frame.pack_propagate(False)
        
        my_ip = self.get_local_ip()
        info_label = tk.Label(info_frame, text=f"🌐 IP الخاص بك: {my_ip} | 📡 المنفذ: {self.DEFAULT_PORT}", 
                             font=('Arial', 10), bg='#ecf0f1')
        info_label.pack(expand=True)
        
        # تبويبات
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True, padx=5, pady=5)
        
        # تبويب إرسال الرسائل
        self.send_frame = ttk.Frame(notebook)
        notebook.add(self.send_frame, text="📤 إرسال رسالة")
        self.create_send_tab()
        
        # تبويب الرسائل المستقبلة
        self.receive_frame = ttk.Frame(notebook)
        notebook.add(self.receive_frame, text="📥 الرسائل المستقبلة")
        self.create_receive_tab()
        
        # تبويب الأجهزة المتصلة
        self.devices_frame = ttk.Frame(notebook)
        notebook.add(self.devices_frame, text="🔍 الأجهزة المتصلة")
        self.create_devices_tab()
    
    def create_send_tab(self):
        """إنشاء تبويب إرسال الرسائل"""
        
        # إطار معلومات المستقبل
        recipient_frame = tk.LabelFrame(self.send_frame, text="📍 معلومات المستقبل", 
                                       font=('Arial', 10, 'bold'), padx=10, pady=10)
        recipient_frame.pack(fill='x', padx=10, pady=5)
        
        # IP المستقبل
        tk.Label(recipient_frame, text="🌐 IP Address:", font=('Arial', 10)).grid(row=0, column=0, sticky='w', pady=2)
        self.target_ip_var = tk.StringVar()
        self.target_ip_entry = tk.Entry(recipient_frame, textvariable=self.target_ip_var, 
                                       font=('Arial', 10), width=20)
        self.target_ip_entry.grid(row=0, column=1, padx=5, pady=2)
        
        # منفذ المستقبل
        tk.Label(recipient_frame, text="📡 Port:", font=('Arial', 10)).grid(row=0, column=2, sticky='w', padx=(20,5), pady=2)
        self.target_port_var = tk.StringVar(value=str(self.DEFAULT_PORT))
        self.target_port_entry = tk.Entry(recipient_frame, textvariable=self.target_port_var, 
                                         font=('Arial', 10), width=10)
        self.target_port_entry.grid(row=0, column=3, padx=5, pady=2)
        
        # أزرار سريعة للأجهزة
        quick_frame = tk.Frame(recipient_frame)
        quick_frame.grid(row=1, column=0, columnspan=4, pady=5)
        
        tk.Button(quick_frame, text="🔍 فحص الأجهزة", command=self.scan_devices,
                 bg='#3498db', fg='white', font=('Arial', 9)).pack(side='left', padx=2)
        tk.Button(quick_frame, text="📋 من القائمة", command=self.select_from_devices,
                 bg='#9b59b6', fg='white', font=('Arial', 9)).pack(side='left', padx=2)
        
        # إطار الرسالة
        message_frame = tk.LabelFrame(self.send_frame, text="✉️ محتوى الرسالة", 
                                     font=('Arial', 10, 'bold'), padx=10, pady=10)
        message_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # اسم المرسل
        sender_frame = tk.Frame(message_frame)
        sender_frame.pack(fill='x', pady=2)
        
        tk.Label(sender_frame, text="👤 اسم المرسل:", font=('Arial', 10)).pack(side='left')
        self.sender_name_var = tk.StringVar(value="مستخدم")
        self.sender_name_entry = tk.Entry(sender_frame, textvariable=self.sender_name_var, 
                                         font=('Arial', 10), width=20)
        self.sender_name_entry.pack(side='left', padx=5)
        
        # عنوان الرسالة
        title_frame = tk.Frame(message_frame)
        title_frame.pack(fill='x', pady=2)
        
        tk.Label(title_frame, text="📝 عنوان الرسالة:", font=('Arial', 10)).pack(side='left')
        self.message_title_var = tk.StringVar()
        self.message_title_entry = tk.Entry(title_frame, textvariable=self.message_title_var, 
                                           font=('Arial', 10), width=40)
        self.message_title_entry.pack(side='left', padx=5)
        
        # نص الرسالة
        tk.Label(message_frame, text="💬 نص الرسالة:", font=('Arial', 10)).pack(anchor='w', pady=(10,2))
        self.message_text = scrolledtext.ScrolledText(message_frame, height=8, font=('Arial', 10))
        self.message_text.pack(fill='both', expand=True, pady=2)
        
        # أزرار الإرسال
        send_buttons_frame = tk.Frame(message_frame)
        send_buttons_frame.pack(fill='x', pady=10)
        
        tk.Button(send_buttons_frame, text="📤 إرسال الرسالة", command=self.send_message,
                 bg='#27ae60', fg='white', font=('Arial', 12, 'bold'), height=2).pack(side='left', padx=5)
        tk.Button(send_buttons_frame, text="🧪 اختبار الاتصال", command=self.test_connection,
                 bg='#f39c12', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        tk.Button(send_buttons_frame, text="🗑️ مسح", command=self.clear_message,
                 bg='#e74c3c', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
    
    def create_receive_tab(self):
        """إنشاء تبويب الرسائل المستقبلة"""
        
        # شريط المعلومات
        info_frame = tk.Frame(self.receive_frame, bg='#2ecc71', height=40)
        info_frame.pack(fill='x', padx=5, pady=5)
        info_frame.pack_propagate(False)
        
        self.listening_status = tk.Label(info_frame, text="🟢 يستمع للرسائل...", 
                                        font=('Arial', 10, 'bold'), fg='white', bg='#2ecc71')
        self.listening_status.pack(expand=True)
        
        # قائمة الرسائل
        messages_frame = tk.LabelFrame(self.receive_frame, text="📥 الرسائل المستقبلة", 
                                      font=('Arial', 10, 'bold'), padx=5, pady=5)
        messages_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # جدول الرسائل
        columns = ('الوقت', 'المرسل', 'IP المرسل', 'العنوان')
        self.messages_tree = ttk.Treeview(messages_frame, columns=columns, show='headings', height=10)
        
        # تعريف الأعمدة
        self.messages_tree.heading('الوقت', text='🕐 الوقت')
        self.messages_tree.heading('المرسل', text='👤 المرسل')
        self.messages_tree.heading('IP المرسل', text='🌐 IP المرسل')
        self.messages_tree.heading('العنوان', text='📝 العنوان')
        
        # تحديد عرض الأعمدة
        self.messages_tree.column('الوقت', width=120)
        self.messages_tree.column('المرسل', width=100)
        self.messages_tree.column('IP المرسل', width=120)
        self.messages_tree.column('العنوان', width=200)
        
        # شريط التمرير
        messages_scrollbar = ttk.Scrollbar(messages_frame, orient='vertical', command=self.messages_tree.yview)
        self.messages_tree.configure(yscrollcommand=messages_scrollbar.set)
        
        self.messages_tree.pack(side='left', fill='both', expand=True)
        messages_scrollbar.pack(side='right', fill='y')
        
        # ربط النقر المزدوج
        self.messages_tree.bind('<Double-1>', self.show_message_details)
        
        # أزرار الإدارة
        buttons_frame = tk.Frame(self.receive_frame)
        buttons_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Button(buttons_frame, text="👁️ عرض الرسالة", command=self.show_selected_message,
                 bg='#3498db', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        tk.Button(buttons_frame, text="↩️ رد على الرسالة", command=self.reply_to_message,
                 bg='#9b59b6', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        tk.Button(buttons_frame, text="🗑️ حذف المحدد", command=self.delete_selected_message,
                 bg='#e74c3c', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        tk.Button(buttons_frame, text="🧹 مسح الكل", command=self.clear_all_messages,
                 bg='#95a5a6', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
    
    def create_devices_tab(self):
        """إنشاء تبويب الأجهزة المتصلة"""
        
        # أزرار الفحص
        scan_frame = tk.Frame(self.devices_frame)
        scan_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Button(scan_frame, text="🔍 فحص الشبكة", command=self.scan_network,
                 bg='#3498db', fg='white', font=('Arial', 12, 'bold')).pack(side='left', padx=5)
        tk.Button(scan_frame, text="🔄 تحديث", command=self.refresh_devices,
                 bg='#27ae60', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        
        self.scan_status = tk.Label(scan_frame, text="جاهز للفحص", font=('Arial', 10))
        self.scan_status.pack(side='left', padx=20)
        
        # قائمة الأجهزة
        devices_frame = tk.LabelFrame(self.devices_frame, text="🖥️ الأجهزة المكتشفة", 
                                     font=('Arial', 10, 'bold'), padx=5, pady=5)
        devices_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # جدول الأجهزة
        device_columns = ('IP Address', 'اسم الجهاز', 'حالة المرسل', 'آخر فحص')
        self.devices_tree = ttk.Treeview(devices_frame, columns=device_columns, show='headings', height=12)
        
        # تعريف الأعمدة
        self.devices_tree.heading('IP Address', text='🌐 IP Address')
        self.devices_tree.heading('اسم الجهاز', text='💻 اسم الجهاز')
        self.devices_tree.heading('حالة المرسل', text='📱 حالة المرسل')
        self.devices_tree.heading('آخر فحص', text='🕐 آخر فحص')
        
        # تحديد عرض الأعمدة
        self.devices_tree.column('IP Address', width=120)
        self.devices_tree.column('اسم الجهاز', width=150)
        self.devices_tree.column('حالة المرسل', width=120)
        self.devices_tree.column('آخر فحص', width=120)
        
        # شريط التمرير للأجهزة
        devices_scrollbar = ttk.Scrollbar(devices_frame, orient='vertical', command=self.devices_tree.yview)
        self.devices_tree.configure(yscrollcommand=devices_scrollbar.set)
        
        self.devices_tree.pack(side='left', fill='both', expand=True)
        devices_scrollbar.pack(side='right', fill='y')
        
        # ربط النقر المزدوج
        self.devices_tree.bind('<Double-1>', self.select_device_for_message)
        
        # أزرار إدارة الأجهزة
        device_buttons_frame = tk.Frame(self.devices_frame)
        device_buttons_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Button(device_buttons_frame, text="📤 إرسال رسالة", command=self.send_to_selected_device,
                 bg='#27ae60', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        tk.Button(device_buttons_frame, text="🧪 اختبار الاتصال", command=self.test_selected_device,
                 bg='#f39c12', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        tk.Button(device_buttons_frame, text="🔍 فحص المرسل", command=self.check_messenger_on_device,
                 bg='#9b59b6', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
    
    def get_local_ip(self):
        """الحصول على IP المحلي"""
        try:
            # إنشاء اتصال وهمي للحصول على IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "127.0.0.1"
    
    def start_listening(self):
        """بدء الاستماع للرسائل الواردة"""
        def listen():
            try:
                self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                self.server_socket.bind(('', self.DEFAULT_PORT))
                self.server_socket.listen(5)
                self.is_listening = True
                
                print(f"🟢 بدء الاستماع على المنفذ {self.DEFAULT_PORT}")
                
                while self.is_listening:
                    try:
                        client_socket, address = self.server_socket.accept()
                        # معالجة الرسالة في thread منفصل
                        threading.Thread(target=self.handle_incoming_message, 
                                       args=(client_socket, address), daemon=True).start()
                    except:
                        break
                        
            except Exception as e:
                print(f"❌ خطأ في الاستماع: {e}")
                self.root.after(0, lambda: self.listening_status.config(
                    text="🔴 خطأ في الاستماع", bg='#e74c3c'))
        
        # بدء الاستماع في thread منفصل
        listen_thread = threading.Thread(target=listen, daemon=True)
        listen_thread.start()
    
    def handle_incoming_message(self, client_socket, address):
        """معالجة الرسالة الواردة"""
        try:
            # استقبال البيانات
            data = client_socket.recv(4096).decode('utf-8')
            message_data = json.loads(data)
            
            # إضافة معلومات إضافية
            message_data['received_time'] = datetime.now().strftime("%H:%M:%S")
            message_data['sender_ip'] = address[0]
            
            # إضافة الرسالة للقائمة
            self.messages.append(message_data)
            
            # تحديث الواجهة
            self.root.after(0, self.update_messages_display)
            
            # إرسال تأكيد الاستلام
            response = {"status": "received", "time": message_data['received_time']}
            client_socket.send(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
            print(f"📥 تم استلام رسالة من {address[0]}: {message_data.get('title', 'بدون عنوان')}")
            
        except Exception as e:
            print(f"❌ خطأ في معالجة الرسالة: {e}")
        finally:
            client_socket.close()
    
    def update_messages_display(self):
        """تحديث عرض الرسائل"""
        # مسح القائمة الحالية
        for item in self.messages_tree.get_children():
            self.messages_tree.delete(item)
        
        # إضافة الرسائل الجديدة
        for msg in reversed(self.messages):  # الأحدث أولاً
            self.messages_tree.insert('', 0, values=(
                msg.get('received_time', ''),
                msg.get('sender_name', 'غير معروف'),
                msg.get('sender_ip', ''),
                msg.get('title', 'بدون عنوان')
            ))
    
    def send_message(self):
        """إرسال رسالة"""
        target_ip = self.target_ip_var.get().strip()
        target_port = self.target_port_var.get().strip()
        sender_name = self.sender_name_var.get().strip()
        message_title = self.message_title_var.get().strip()
        message_content = self.message_text.get(1.0, tk.END).strip()
        
        # التحقق من البيانات
        if not target_ip:
            messagebox.showerror("خطأ", "يرجى إدخال IP المستقبل")
            return
        
        if not message_content:
            messagebox.showerror("خطأ", "يرجى إدخال نص الرسالة")
            return
        
        try:
            port = int(target_port) if target_port else self.DEFAULT_PORT
        except ValueError:
            messagebox.showerror("خطأ", "رقم المنفذ غير صحيح")
            return
        
        # إعداد بيانات الرسالة
        message_data = {
            "sender_name": sender_name or "مستخدم",
            "title": message_title or "رسالة",
            "content": message_content,
            "sent_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "sender_ip": self.get_local_ip()
        }
        
        # إرسال الرسالة في thread منفصل
        def send():
            try:
                client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                client_socket.settimeout(10)  # timeout 10 ثواني
                
                # الاتصال بالجهاز المستهدف
                client_socket.connect((target_ip, port))
                
                # إرسال البيانات
                data = json.dumps(message_data, ensure_ascii=False)
                client_socket.send(data.encode('utf-8'))
                
                # انتظار تأكيد الاستلام
                response = client_socket.recv(1024).decode('utf-8')
                response_data = json.loads(response)
                
                client_socket.close()
                
                # إظهار نتيجة الإرسال
                if response_data.get('status') == 'received':
                    self.root.after(0, lambda: messagebox.showinfo("نجح الإرسال! ✅", 
                        f"تم إرسال الرسالة بنجاح إلى {target_ip}\n"
                        f"وقت الاستلام: {response_data.get('time', 'غير معروف')}"))
                    self.root.after(0, self.clear_message)
                else:
                    self.root.after(0, lambda: messagebox.showwarning("تحذير", 
                        "تم إرسال الرسالة ولكن لم يتم تأكيد الاستلام"))
                
            except socket.timeout:
                self.root.after(0, lambda: messagebox.showerror("خطأ في الاتصال", 
                    f"انتهت مهلة الاتصال مع {target_ip}\n"
                    f"تأكد من:\n"
                    f"• الجهاز متصل بالشبكة\n"
                    f"• تطبيق المرسل يعمل على الجهاز\n"
                    f"• المنفذ {port} مفتوح"))
            except ConnectionRefusedError:
                self.root.after(0, lambda: messagebox.showerror("رفض الاتصال", 
                    f"رفض الجهاز {target_ip} الاتصال\n"
                    f"تأكد من تشغيل تطبيق المرسل على الجهاز المستهدف"))
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("خطأ في الإرسال", 
                    f"فشل في إرسال الرسالة إلى {target_ip}\n"
                    f"الخطأ: {str(e)}"))
        
        # بدء الإرسال
        send_thread = threading.Thread(target=send, daemon=True)
        send_thread.start()
    
    def test_connection(self):
        """اختبار الاتصال مع الجهاز"""
        target_ip = self.target_ip_var.get().strip()
        if not target_ip:
            messagebox.showerror("خطأ", "يرجى إدخال IP المستقبل")
            return
        
        def test():
            try:
                # اختبار ping
                result = subprocess.run(['ping', '-n', '1', target_ip], 
                                      capture_output=True, text=True, timeout=5)
                
                if result.returncode == 0:
                    # اختبار المنفذ
                    try:
                        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        test_socket.settimeout(3)
                        port = int(self.target_port_var.get() or self.DEFAULT_PORT)
                        test_socket.connect((target_ip, port))
                        test_socket.close()
                        
                        self.root.after(0, lambda: messagebox.showinfo("نجح الاختبار! ✅", 
                            f"الجهاز {target_ip} متاح ويستمع على المنفذ {port}\n"
                            f"يمكنك إرسال الرسائل بأمان"))
                    except:
                        self.root.after(0, lambda: messagebox.showwarning("اتصال جزئي ⚠️", 
                            f"الجهاز {target_ip} متصل بالشبكة\n"
                            f"لكن تطبيق المرسل غير مفعل على المنفذ {port}"))
                else:
                    self.root.after(0, lambda: messagebox.showerror("فشل الاختبار ❌", 
                        f"الجهاز {target_ip} غير متاح\n"
                        f"تأكد من اتصاله بالشبكة"))
                        
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("خطأ في الاختبار", 
                    f"فشل في اختبار الاتصال: {str(e)}"))
        
        test_thread = threading.Thread(target=test, daemon=True)
        test_thread.start()
    
    def clear_message(self):
        """مسح محتوى الرسالة"""
        self.message_title_var.set("")
        self.message_text.delete(1.0, tk.END)

    def show_message_details(self, event):
        """عرض تفاصيل الرسالة المحددة"""
        self.show_selected_message()

    def show_selected_message(self):
        """عرض الرسالة المحددة"""
        selection = self.messages_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد رسالة لعرضها")
            return

        # الحصول على فهرس الرسالة
        item = self.messages_tree.item(selection[0])
        values = item['values']

        # البحث عن الرسالة في القائمة
        sender_ip = values[2]
        received_time = values[0]
        title = values[3]

        message_data = None
        for msg in self.messages:
            if (msg.get('sender_ip') == sender_ip and
                msg.get('received_time') == received_time and
                msg.get('title') == title):
                message_data = msg
                break

        if not message_data:
            messagebox.showerror("خطأ", "لم يتم العثور على الرسالة")
            return

        # إنشاء نافذة عرض الرسالة
        msg_window = tk.Toplevel(self.root)
        msg_window.title(f"📧 رسالة من {message_data.get('sender_name', 'غير معروف')}")
        msg_window.geometry("600x500")
        msg_window.configure(bg='#f8f9fa')

        # معلومات الرسالة
        info_frame = tk.Frame(msg_window, bg='#e9ecef', relief='raised', bd=1)
        info_frame.pack(fill='x', padx=10, pady=10)

        tk.Label(info_frame, text=f"👤 المرسل: {message_data.get('sender_name', 'غير معروف')}",
                font=('Arial', 10, 'bold'), bg='#e9ecef').pack(anchor='w', padx=10, pady=2)
        tk.Label(info_frame, text=f"🌐 IP المرسل: {message_data.get('sender_ip', '')}",
                font=('Arial', 10), bg='#e9ecef').pack(anchor='w', padx=10, pady=2)
        tk.Label(info_frame, text=f"🕐 وقت الإرسال: {message_data.get('sent_time', '')}",
                font=('Arial', 10), bg='#e9ecef').pack(anchor='w', padx=10, pady=2)
        tk.Label(info_frame, text=f"📥 وقت الاستلام: {message_data.get('received_time', '')}",
                font=('Arial', 10), bg='#e9ecef').pack(anchor='w', padx=10, pady=2)

        # عنوان الرسالة
        title_frame = tk.Frame(msg_window, bg='#f8f9fa')
        title_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(title_frame, text="📝 العنوان:", font=('Arial', 10, 'bold'), bg='#f8f9fa').pack(anchor='w')
        title_text = tk.Text(title_frame, height=2, font=('Arial', 12, 'bold'), bg='#fff3cd',
                            relief='solid', bd=1, wrap='word')
        title_text.pack(fill='x', pady=2)
        title_text.insert(1.0, message_data.get('title', 'بدون عنوان'))
        title_text.config(state='disabled')

        # محتوى الرسالة
        content_frame = tk.Frame(msg_window, bg='#f8f9fa')
        content_frame.pack(fill='both', expand=True, padx=10, pady=5)

        tk.Label(content_frame, text="💬 محتوى الرسالة:", font=('Arial', 10, 'bold'), bg='#f8f9fa').pack(anchor='w')
        content_text = scrolledtext.ScrolledText(content_frame, font=('Arial', 11), bg='white',
                                               relief='solid', bd=1, wrap='word')
        content_text.pack(fill='both', expand=True, pady=2)
        content_text.insert(1.0, message_data.get('content', ''))
        content_text.config(state='disabled')

        # أزرار الإجراءات
        buttons_frame = tk.Frame(msg_window, bg='#f8f9fa')
        buttons_frame.pack(fill='x', padx=10, pady=10)

        tk.Button(buttons_frame, text="↩️ رد على الرسالة",
                 command=lambda: self.reply_to_message_data(message_data, msg_window),
                 bg='#28a745', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        tk.Button(buttons_frame, text="📋 نسخ المحتوى",
                 command=lambda: self.copy_message_content(message_data),
                 bg='#17a2b8', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        tk.Button(buttons_frame, text="🗑️ حذف الرسالة",
                 command=lambda: self.delete_message_data(message_data, msg_window),
                 bg='#dc3545', fg='white', font=('Arial', 10)).pack(side='left', padx=5)
        tk.Button(buttons_frame, text="❌ إغلاق", command=msg_window.destroy,
                 bg='#6c757d', fg='white', font=('Arial', 10)).pack(side='right', padx=5)

    def reply_to_message(self):
        """الرد على الرسالة المحددة"""
        selection = self.messages_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد رسالة للرد عليها")
            return

        # الحصول على معلومات المرسل
        item = self.messages_tree.item(selection[0])
        values = item['values']
        sender_ip = values[2]
        sender_name = values[1]
        original_title = values[3]

        # الانتقال لتبويب الإرسال وملء البيانات
        self.target_ip_var.set(sender_ip)
        self.message_title_var.set(f"رد: {original_title}")

        # التبديل للتبويب الأول
        notebook = self.root.children['!notebook']
        notebook.select(0)

        messagebox.showinfo("جاهز للرد", f"تم تعبئة بيانات الرد إلى {sender_name} ({sender_ip})")

    def reply_to_message_data(self, message_data, parent_window):
        """الرد على رسالة محددة"""
        self.target_ip_var.set(message_data.get('sender_ip', ''))
        self.message_title_var.set(f"رد: {message_data.get('title', '')}")

        # التبديل للتبويب الأول
        notebook = self.root.children['!notebook']
        notebook.select(0)

        parent_window.destroy()
        messagebox.showinfo("جاهز للرد", f"تم تعبئة بيانات الرد إلى {message_data.get('sender_name', 'غير معروف')}")

    def copy_message_content(self, message_data):
        """نسخ محتوى الرسالة"""
        content = f"من: {message_data.get('sender_name', 'غير معروف')}\n"
        content += f"العنوان: {message_data.get('title', '')}\n"
        content += f"المحتوى:\n{message_data.get('content', '')}"

        self.root.clipboard_clear()
        self.root.clipboard_append(content)
        messagebox.showinfo("تم النسخ", "تم نسخ محتوى الرسالة إلى الحافظة")

    def delete_selected_message(self):
        """حذف الرسالة المحددة"""
        selection = self.messages_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد رسالة لحذفها")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل تريد حذف الرسالة المحددة؟"):
            # الحصول على معلومات الرسالة
            item = self.messages_tree.item(selection[0])
            values = item['values']

            # البحث وحذف الرسالة
            for i, msg in enumerate(self.messages):
                if (msg.get('sender_ip') == values[2] and
                    msg.get('received_time') == values[0] and
                    msg.get('title') == values[3]):
                    del self.messages[i]
                    break

            # تحديث العرض
            self.update_messages_display()
            messagebox.showinfo("تم الحذف", "تم حذف الرسالة بنجاح")

    def delete_message_data(self, message_data, parent_window):
        """حذف رسالة محددة"""
        if messagebox.askyesno("تأكيد الحذف", "هل تريد حذف هذه الرسالة؟"):
            if message_data in self.messages:
                self.messages.remove(message_data)
                self.update_messages_display()
                parent_window.destroy()
                messagebox.showinfo("تم الحذف", "تم حذف الرسالة بنجاح")

    def clear_all_messages(self):
        """مسح جميع الرسائل"""
        if messagebox.askyesno("تأكيد المسح", "هل تريد حذف جميع الرسائل؟"):
            self.messages.clear()
            self.update_messages_display()
            messagebox.showinfo("تم المسح", "تم حذف جميع الرسائل")

    def scan_devices(self):
        """فحص الأجهزة المتصلة (مبسط)"""
        self.scan_network()

    def select_from_devices(self):
        """اختيار جهاز من قائمة الأجهزة"""
        # التبديل لتبويب الأجهزة
        notebook = self.root.children['!notebook']
        notebook.select(2)
        messagebox.showinfo("اختيار جهاز", "انقر مرتين على أي جهاز في قائمة الأجهزة لاختياره")

    def scan_network(self):
        """فحص الشبكة للأجهزة المتصلة"""
        def scan():
            try:
                self.root.after(0, lambda: self.scan_status.config(text="🔍 جاري فحص الشبكة..."))

                # الحصول على نطاق الشبكة
                local_ip = self.get_local_ip()
                network_base = '.'.join(local_ip.split('.')[:-1]) + '.'

                # مسح القائمة الحالية
                self.root.after(0, self.clear_devices_list)

                # فحص الأجهزة
                active_devices = []
                for i in range(1, 255):
                    ip = network_base + str(i)

                    # اختبار ping
                    try:
                        result = subprocess.run(['ping', '-n', '1', '-w', '1000', ip],
                                              capture_output=True, text=True, timeout=2)
                        if result.returncode == 0:
                            # محاولة الحصول على اسم الجهاز
                            try:
                                hostname = socket.gethostbyaddr(ip)[0]
                            except:
                                hostname = "غير معروف"

                            # فحص حالة المرسل
                            messenger_status = self.check_messenger_status(ip)

                            device_info = {
                                'ip': ip,
                                'hostname': hostname,
                                'messenger_status': messenger_status,
                                'last_scan': datetime.now().strftime("%H:%M:%S")
                            }
                            active_devices.append(device_info)

                            # تحديث العرض فوري
                            self.root.after(0, lambda dev=device_info: self.add_device_to_list(dev))

                    except:
                        continue

                self.root.after(0, lambda: self.scan_status.config(
                    text=f"✅ تم العثور على {len(active_devices)} جهاز"))

            except Exception as e:
                self.root.after(0, lambda: self.scan_status.config(text=f"❌ خطأ في الفحص: {str(e)}"))

        # بدء الفحص في thread منفصل
        scan_thread = threading.Thread(target=scan, daemon=True)
        scan_thread.start()

    def clear_devices_list(self):
        """مسح قائمة الأجهزة"""
        for item in self.devices_tree.get_children():
            self.devices_tree.delete(item)

    def add_device_to_list(self, device_info):
        """إضافة جهاز لقائمة الأجهزة"""
        self.devices_tree.insert('', 'end', values=(
            device_info['ip'],
            device_info['hostname'],
            device_info['messenger_status'],
            device_info['last_scan']
        ))

    def check_messenger_status(self, ip):
        """فحص حالة المرسل على الجهاز"""
        try:
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.settimeout(1)
            test_socket.connect((ip, self.DEFAULT_PORT))
            test_socket.close()
            return "🟢 متاح"
        except:
            return "🔴 غير متاح"

    def refresh_devices(self):
        """تحديث قائمة الأجهزة"""
        self.scan_network()

    def select_device_for_message(self, event):
        """اختيار جهاز لإرسال رسالة إليه"""
        selection = self.devices_tree.selection()
        if not selection:
            return

        item = self.devices_tree.item(selection[0])
        device_ip = item['values'][0]

        # تعبئة IP في تبويب الإرسال
        self.target_ip_var.set(device_ip)

        # التبديل لتبويب الإرسال
        notebook = self.root.children['!notebook']
        notebook.select(0)

        messagebox.showinfo("تم الاختيار", f"تم اختيار الجهاز {device_ip}\nيمكنك الآن كتابة رسالتك")

    def send_to_selected_device(self):
        """إرسال رسالة للجهاز المحدد"""
        selection = self.devices_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد جهاز من القائمة")
            return

        self.select_device_for_message(None)

    def test_selected_device(self):
        """اختبار الجهاز المحدد"""
        selection = self.devices_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد جهاز من القائمة")
            return

        item = self.devices_tree.item(selection[0])
        device_ip = item['values'][0]

        self.target_ip_var.set(device_ip)
        self.test_connection()

    def check_messenger_on_device(self):
        """فحص حالة المرسل على الجهاز المحدد"""
        selection = self.devices_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد جهاز من القائمة")
            return

        item = self.devices_tree.item(selection[0])
        device_ip = item['values'][0]

        status = self.check_messenger_status(device_ip)

        if "متاح" in status:
            messagebox.showinfo("حالة المرسل ✅", f"تطبيق المرسل يعمل على الجهاز {device_ip}\nيمكنك إرسال الرسائل بأمان")
        else:
            messagebox.showwarning("حالة المرسل ❌", f"تطبيق المرسل لا يعمل على الجهاز {device_ip}\nتأكد من تشغيل التطبيق على الجهاز المستهدف")

    def run(self):
        """تشغيل التطبيق"""
        try:
            self.root.mainloop()
        finally:
            # إغلاق الخادم عند إغلاق التطبيق
            self.is_listening = False
            if self.server_socket:
                self.server_socket.close()

if __name__ == "__main__":
    try:
        app = NetworkMessenger()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")
