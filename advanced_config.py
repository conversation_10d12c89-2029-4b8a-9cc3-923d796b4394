"""
إعدادات متقدمة لـ Steam Lua Injector
يمكن للمستخدمين المتقدمين تعديل هذه الإعدادات
"""

# مسارات Steam المخصصة (إضافة مسارات أخرى إذا لزم الأمر)
CUSTOM_STEAM_PATHS = [
    r"D:\Games\Steam",
    r"E:\Steam",
    r"F:\Steam",
    r"G:\Steam"
]

# امتدادات الملفات المدعومة للبحث
SUPPORTED_EXTENSIONS = [
    '.lua',
    '.luac',  # ملفات Lua مترجمة
    '.txt',   # أحياناً تكون ملفات النصوص تحتوي على Lua
]

# إعدادات النسخ الاحتياطية
BACKUP_SETTINGS = {
    'max_backups': 10,  # أقصى عدد نسخ احتياطية لكل ملف
    'backup_extension': '.backup',
    'timestamp_format': '%Y%m%d_%H%M%S'
}

# إعدادات البحث
SEARCH_SETTINGS = {
    'max_depth': 5,  # أقصى عمق للبحث في المجلدات الفرعية
    'exclude_folders': [
        '__pycache__',
        '.git',
        '.svn',
        'node_modules',
        'temp',
        'cache'
    ],
    'case_sensitive': False
}

# إعدادات الواجهة
UI_SETTINGS = {
    'window_size': '800x600',
    'font_family': 'Arial',
    'font_size': 10,
    'theme': 'default',  # يمكن إضافة themes أخرى لاحقاً
    'language': 'ar'  # العربية افتراضياً
}

# إعدادات الأمان
SECURITY_SETTINGS = {
    'require_confirmation': True,  # طلب تأكيد قبل التعديلات
    'auto_backup': True,  # إنشاء نسخة احتياطية تلقائياً
    'verify_file_integrity': True,  # التحقق من سلامة الملفات
    'log_operations': True  # تسجيل العمليات
}

# قائمة الألعاب المعروفة (للمساعدة في البحث)
KNOWN_GAMES = {
    '730': {
        'name': 'Counter-Strike 2',
        'lua_folders': ['scripts', 'cfg'],
        'common_files': ['autoexec.cfg', 'config.cfg']
    },
    '570': {
        'name': 'Dota 2',
        'lua_folders': ['game/dota/scripts', 'game/dota/pak01_dir'],
        'common_files': ['addon_game_mode.lua', 'barebones.lua']
    },
    '440': {
        'name': 'Team Fortress 2',
        'lua_folders': ['tf/scripts'],
        'common_files': ['hudlayout.res']
    },
    '550': {
        'name': 'Left 4 Dead 2',
        'lua_folders': ['left4dead2/scripts'],
        'common_files': ['gameinfo.txt']
    }
}

# إعدادات التسجيل
LOGGING_SETTINGS = {
    'log_file': 'steam_injector.log',
    'log_level': 'INFO',  # DEBUG, INFO, WARNING, ERROR
    'max_log_size': 10 * 1024 * 1024,  # 10 MB
    'backup_count': 5
}

# إعدادات الشبكة (للميزات المستقبلية)
NETWORK_SETTINGS = {
    'enable_updates': False,  # تحديثات تلقائية
    'update_url': '',
    'timeout': 30
}

def get_setting(category, key, default=None):
    """الحصول على إعداد معين"""
    settings_map = {
        'backup': BACKUP_SETTINGS,
        'search': SEARCH_SETTINGS,
        'ui': UI_SETTINGS,
        'security': SECURITY_SETTINGS,
        'logging': LOGGING_SETTINGS,
        'network': NETWORK_SETTINGS
    }
    
    if category in settings_map:
        return settings_map[category].get(key, default)
    return default

def update_setting(category, key, value):
    """تحديث إعداد معين"""
    settings_map = {
        'backup': BACKUP_SETTINGS,
        'search': SEARCH_SETTINGS,
        'ui': UI_SETTINGS,
        'security': SECURITY_SETTINGS,
        'logging': LOGGING_SETTINGS,
        'network': NETWORK_SETTINGS
    }
    
    if category in settings_map:
        settings_map[category][key] = value
        return True
    return False
