# 📱 مرسل الرسائل الشبكي - Network Messenger

## 🌟 **نظام رسائل متكامل للشبكة المحلية**

تطبيق شامل لإرسال واستقبال الرسائل بين الأجهزة المتصلة بنفس الشبكة المحلية باستخدام IP Address.

---

## ✨ **الميزات الرئيسية**

### 📤 **إرسال الرسائل:**
- **إرسال فوري** للرسائل عبر IP
- **تأكيد الاستلام** من الجهاز المستهدف
- **اختبار الاتصال** قبل الإرسال
- **رسائل مخصصة** مع عنوان ومحتوى

### 📥 **استقبال الرسائل:**
- **استقبال تلقائي** للرسائل الواردة
- **عرض مفصل** لجميع الرسائل
- **إدارة شاملة** للرسائل المستقبلة
- **رد سريع** على الرسائل

### 🔍 **اكتشاف الأجهزة:**
- **فحص الشبكة** للأجهزة المتصلة
- **اكتشاف المرسلين** النشطين
- **اختيار سريع** للأجهزة المكتشفة
- **فحص حالة المرسل** على كل جهاز

### 🎨 **واجهة متقدمة:**
- **تصميم عصري** وسهل الاستخدام
- **تبويبات منظمة** للوظائف المختلفة
- **رسائل واضحة** للحالة والأخطاء
- **دعم كامل للغة العربية**

---

## 🚀 **التشغيل السريع**

### **الطريقة الأسهل:**
```bash
run_messenger.bat
```

### **أو يدوياً:**
```bash
python network_messenger.py
```

---

## 📋 **المتطلبات**

### **النظام:**
- **Windows, Linux, أو macOS**
- **Python 3.7+**
- **اتصال بالشبكة المحلية**

### **المكتبات:**
- **tkinter** (مدمجة مع Python)
- **socket** (مدمجة مع Python)
- **json** (مدمجة مع Python)
- **threading** (مدمجة مع Python)

---

## 📁 **هيكل المشروع**

```
network_messenger/
├── network_messenger.py      # التطبيق الرئيسي
├── test_messenger.py         # أداة الاختبار
├── run_messenger.bat         # تشغيل سريع
├── test_messenger.bat        # اختبار سريع
├── MESSENGER_GUIDE.md        # دليل الاستخدام التفصيلي
└── MESSENGER_README.md       # هذا الملف
```

---

## 🎮 **كيفية الاستخدام**

### **1. إرسال رسالة:**
1. **شغل التطبيق** على جهازك
2. **أدخل IP الجهاز المستهدف** في تبويب "إرسال رسالة"
3. **اكتب اسمك وعنوان الرسالة ومحتواها**
4. **اضغط "إرسال الرسالة"**
5. **انتظر تأكيد الوصول**

### **2. استقبال الرسائل:**
1. **التطبيق يستقبل تلقائياً** عند التشغيل
2. **الرسائل تظهر في تبويب "الرسائل المستقبلة"**
3. **انقر مرتين على أي رسالة** لعرض التفاصيل
4. **يمكنك الرد مباشرة** من نافذة الرسالة

### **3. اكتشاف الأجهزة:**
1. **اذهب لتبويب "الأجهزة المتصلة"**
2. **اضغط "فحص الشبكة"**
3. **انتظر اكتمال الفحص**
4. **انقر مرتين على أي جهاز** لإرسال رسالة إليه

---

## 🧪 **الاختبار**

### **اختبار شامل:**
```bash
test_messenger.bat
```

### **اختبار جهاز محدد:**
```bash
test_messenger.bat ************0
```

### **اختبار تفاعلي:**
```bash
python test_messenger.py
```

---

## 🔧 **استكشاف الأخطاء**

### **مشاكل شائعة:**

#### **"رفض الاتصال"**
- **السبب**: المرسل لا يعمل على الجهاز المستهدف
- **الحل**: تأكد من تشغيل التطبيق على الجهاز المستهدف

#### **"انتهت مهلة الاتصال"**
- **السبب**: الجهاز بطيء أو غير متصل
- **الحل**: تحقق من اتصال الشبكة

#### **"الجهاز غير متاح"**
- **السبب**: IP خاطئ أو الجهاز مغلق
- **الحل**: تأكد من صحة IP وتشغيل الجهاز

#### **"خطأ في الاستماع"**
- **السبب**: المنفذ مستخدم من تطبيق آخر
- **الحل**: أغلق التطبيقات الأخرى أو غير المنفذ

---

## 🛡️ **الأمان والخصوصية**

### **⚠️ تحذيرات مهمة:**
- **الشبكة المحلية فقط** - لا يعمل عبر الإنترنت
- **لا توجد تشفير** - الرسائل واضحة
- **لا توجد مصادقة** - أي شخص يمكنه الإرسال
- **لا يحفظ الرسائل** - تختفي عند إغلاق التطبيق

### **✅ الاستخدام الآمن:**
- **استخدم على الشبكات الموثوقة فقط**
- **لا ترسل معلومات حساسة**
- **تأكد من هوية المرسل**

---

## 💡 **نصائح للاستخدام الأمثل**

### **للحصول على أفضل النتائج:**
- ✅ **شغل التطبيق على جميع الأجهزة** المشاركة
- ✅ **استخدم أسماء واضحة** للمرسلين
- ✅ **اكتب عناوين مفيدة** للرسائل
- ✅ **اختبر الاتصال** قبل الإرسال

### **تجنب هذه الأخطاء:**
- ❌ **لا تستخدم على شبكات عامة**
- ❌ **لا تعتمد عليه للرسائل المهمة**
- ❌ **لا ترسل معلومات شخصية**

---

## 🌟 **أمثلة عملية**

### **📱 رسالة شخصية:**
```
IP: ************0
المرسل: أحمد
العنوان: مرحبا
المحتوى: كيف حالك؟ هل تريد الذهاب للمطعم؟
```

### **💼 رسالة عمل:**
```
IP: ************
المرسل: مدير المشروع
العنوان: اجتماع طارئ
المحتوى: اجتماع في قاعة الاجتماعات الساعة 3 عصراً
```

### **🎮 دعوة لعب:**
```
IP: ************
المرسل: صديقك
العنوان: دعوة لعب
المحتوى: انضم للعبة على الخادم ************
```

---

## 🔄 **التطويرات المستقبلية**

### **ميزات مخططة:**
- [ ] **تشفير الرسائل** للأمان
- [ ] **حفظ الرسائل** في قاعدة بيانات
- [ ] **إرسال ملفات** مع الرسائل
- [ ] **مجموعات الدردشة** الجماعية
- [ ] **إشعارات صوتية** للرسائل الجديدة
- [ ] **واجهة ويب** للاستخدام من المتصفح
- [ ] **تطبيق موبايل** للهواتف الذكية

---

## 🤝 **المساهمة**

### **كيفية المساهمة:**
- **اقتراح ميزات جديدة**
- **الإبلاغ عن الأخطاء**
- **تحسين الواجهة**
- **كتابة الوثائق**
- **اختبار على أنظمة مختلفة**

### **للمطورين:**
- **الكود مفتوح المصدر**
- **مكتوب بـ Python البسيط**
- **يمكن تعديله بسهولة**
- **موثق بالتفصيل**

---

## 📞 **الدعم والمساعدة**

### **للحصول على المساعدة:**
1. **اقرأ `MESSENGER_GUIDE.md`** للدليل التفصيلي
2. **جرب `test_messenger.bat`** لتشخيص المشاكل
3. **تحقق من اتصال الشبكة**
4. **تأكد من تشغيل التطبيق على الأجهزة المستهدفة**

### **للتطوير:**
- **الكود موثق بالتفصيل**
- **استخدام مكتبات Python القياسية**
- **تصميم مودولي قابل للتوسع**

---

## 📊 **معلومات تقنية**

### **الشبكة:**
- **البروتوكول**: TCP
- **المنفذ الافتراضي**: 9999
- **تنسيق البيانات**: JSON
- **التشفير**: لا يوجد

### **الأداء:**
- **استهلاك الذاكرة**: منخفض (~10-20 MB)
- **استهلاك المعالج**: منخفض جداً
- **سرعة الإرسال**: فورية على الشبكة المحلية
- **عدد الرسائل**: غير محدود (محدود بالذاكرة)

---

## 📄 **الترخيص**

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.
استخدمه بمسؤولية واحترم خصوصية الآخرين.

---

**🎉 استمتع بالتواصل السريع والفعال عبر شبكتك المحلية!**

**📱 مرسل الرسائل الشبكي - تواصل بلا حدود!**

---

## 🚀 **ابدأ الآن:**

```bash
# 1. شغل المرسل
run_messenger.bat

# 2. اختبر النظام
test_messenger.bat

# 3. ابدأ التواصل!
```
