"""
مراقب استهلاك البيانات
"""
import psutil
import time
import threading
from collections import defaultdict, deque
import json
import os

class BandwidthMonitor:
    def __init__(self):
        self.monitoring = False
        self.device_usage = defaultdict(lambda: {
            'download': 0,
            'upload': 0,
            'total': 0,
            'history': deque(maxlen=100)  # آخر 100 قراءة
        })
        self.network_interfaces = {}
        self.start_time = time.time()
        self.last_stats = {}
        self.data_file = "network_usage.json"
        
        # تحميل البيانات المحفوظة
        self.load_usage_data()
        
    def get_network_interfaces(self):
        """الحصول على واجهات الشبكة"""
        interfaces = psutil.net_if_addrs()
        stats = psutil.net_if_stats()
        
        active_interfaces = {}
        for interface, addresses in interfaces.items():
            if interface in stats and stats[interface].isup:
                for addr in addresses:
                    if addr.family == 2:  # IPv4
                        active_interfaces[interface] = {
                            'ip': addr.address,
                            'netmask': addr.netmask,
                            'is_up': stats[interface].isup,
                            'speed': stats[interface].speed
                        }
                        break
        
        return active_interfaces
    
    def get_current_usage(self):
        """الحصول على الاستخدام الحالي"""
        current_stats = psutil.net_io_counters(pernic=True)
        current_time = time.time()
        
        if not self.last_stats:
            self.last_stats = current_stats
            return {}
        
        usage_data = {}
        time_diff = current_time - getattr(self, 'last_time', current_time - 1)
        
        for interface, stats in current_stats.items():
            if interface in self.last_stats:
                last_stats = self.last_stats[interface]
                
                # حساب الفرق في البيانات
                bytes_sent_diff = stats.bytes_sent - last_stats.bytes_sent
                bytes_recv_diff = stats.bytes_recv - last_stats.bytes_recv
                
                # حساب السرعة (بايت/ثانية)
                upload_speed = bytes_sent_diff / time_diff if time_diff > 0 else 0
                download_speed = bytes_recv_diff / time_diff if time_diff > 0 else 0
                
                usage_data[interface] = {
                    'upload_speed': upload_speed,
                    'download_speed': download_speed,
                    'total_upload': stats.bytes_sent,
                    'total_download': stats.bytes_recv,
                    'total_packets_sent': stats.packets_sent,
                    'total_packets_recv': stats.packets_recv,
                    'errors_in': stats.errin,
                    'errors_out': stats.errout,
                    'drops_in': stats.dropin,
                    'drops_out': stats.dropout
                }
        
        self.last_stats = current_stats
        self.last_time = current_time
        return usage_data
    
    def format_bytes(self, bytes_value):
        """تنسيق البايتات إلى وحدات قابلة للقراءة"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.2f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.2f} PB"
    
    def format_speed(self, bytes_per_second):
        """تنسيق السرعة"""
        return f"{self.format_bytes(bytes_per_second)}/s"
    
    def monitor_bandwidth(self, callback=None, interval=1):
        """مراقبة استهلاك البيانات"""
        self.monitoring = True
        
        while self.monitoring:
            try:
                usage_data = self.get_current_usage()
                
                # حفظ البيانات في التاريخ
                timestamp = time.time()
                for interface, data in usage_data.items():
                    history_entry = {
                        'timestamp': timestamp,
                        'upload_speed': data['upload_speed'],
                        'download_speed': data['download_speed'],
                        'total_upload': data['total_upload'],
                        'total_download': data['total_download']
                    }
                    
                    self.device_usage[interface]['history'].append(history_entry)
                    self.device_usage[interface]['download'] = data['total_download']
                    self.device_usage[interface]['upload'] = data['total_upload']
                    self.device_usage[interface]['total'] = data['total_download'] + data['total_upload']
                
                # استدعاء callback إذا كان موجود
                if callback:
                    callback(usage_data)
                
                # حفظ البيانات كل 10 ثوان
                if int(timestamp) % 10 == 0:
                    self.save_usage_data()
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"خطأ في مراقبة البيانات: {e}")
                time.sleep(interval)
    
    def start_monitoring(self, callback=None, interval=1):
        """بدء مراقبة البيانات في خيط منفصل"""
        if not self.monitoring:
            monitor_thread = threading.Thread(
                target=self.monitor_bandwidth,
                args=(callback, interval)
            )
            monitor_thread.daemon = True
            monitor_thread.start()
    
    def stop_monitoring(self):
        """إيقاف مراقبة البيانات"""
        self.monitoring = False
        self.save_usage_data()
    
    def get_interface_summary(self, interface):
        """الحصول على ملخص لواجهة معينة"""
        if interface not in self.device_usage:
            return None
        
        data = self.device_usage[interface]
        history = list(data['history'])
        
        if not history:
            return {
                'interface': interface,
                'total_download': self.format_bytes(data['download']),
                'total_upload': self.format_bytes(data['upload']),
                'total_usage': self.format_bytes(data['total']),
                'current_download_speed': "0 B/s",
                'current_upload_speed': "0 B/s",
                'peak_download_speed': "0 B/s",
                'peak_upload_speed': "0 B/s"
            }
        
        # حساب السرعات الحالية والقصوى
        current_entry = history[-1]
        peak_download = max(entry['download_speed'] for entry in history)
        peak_upload = max(entry['upload_speed'] for entry in history)
        
        return {
            'interface': interface,
            'total_download': self.format_bytes(data['download']),
            'total_upload': self.format_bytes(data['upload']),
            'total_usage': self.format_bytes(data['total']),
            'current_download_speed': self.format_speed(current_entry['download_speed']),
            'current_upload_speed': self.format_speed(current_entry['upload_speed']),
            'peak_download_speed': self.format_speed(peak_download),
            'peak_upload_speed': self.format_speed(peak_upload),
            'session_time': self.format_time(time.time() - self.start_time)
        }
    
    def format_time(self, seconds):
        """تنسيق الوقت"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def get_all_interfaces_summary(self):
        """الحصول على ملخص جميع الواجهات"""
        interfaces = self.get_network_interfaces()
        summaries = {}
        
        for interface in interfaces:
            summary = self.get_interface_summary(interface)
            if summary:
                summaries[interface] = summary
        
        return summaries
    
    def get_usage_history(self, interface, hours=24):
        """الحصول على تاريخ الاستخدام لفترة معينة"""
        if interface not in self.device_usage:
            return []
        
        history = list(self.device_usage[interface]['history'])
        cutoff_time = time.time() - (hours * 3600)
        
        # تصفية البيانات حسب الوقت
        filtered_history = [
            entry for entry in history 
            if entry['timestamp'] >= cutoff_time
        ]
        
        return filtered_history
    
    def save_usage_data(self):
        """حفظ بيانات الاستخدام"""
        try:
            # تحويل deque إلى list للحفظ
            save_data = {}
            for interface, data in self.device_usage.items():
                save_data[interface] = {
                    'download': data['download'],
                    'upload': data['upload'],
                    'total': data['total'],
                    'history': list(data['history'])
                }
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ البيانات: {e}")
    
    def load_usage_data(self):
        """تحميل بيانات الاستخدام المحفوظة"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    save_data = json.load(f)
                
                for interface, data in save_data.items():
                    self.device_usage[interface]['download'] = data.get('download', 0)
                    self.device_usage[interface]['upload'] = data.get('upload', 0)
                    self.device_usage[interface]['total'] = data.get('total', 0)
                    
                    # تحويل list إلى deque
                    history = data.get('history', [])
                    self.device_usage[interface]['history'] = deque(history, maxlen=100)
                    
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
    
    def reset_usage_data(self):
        """إعادة تعيين بيانات الاستخدام"""
        self.device_usage.clear()
        self.start_time = time.time()
        if os.path.exists(self.data_file):
            os.remove(self.data_file)
    
    def get_top_interfaces(self, limit=5):
        """الحصول على أكثر الواجهات استخداماً"""
        interfaces = [(interface, data['total']) for interface, data in self.device_usage.items()]
        interfaces.sort(key=lambda x: x[1], reverse=True)
        return interfaces[:limit]
