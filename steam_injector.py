"""
كلاس رئيسي لـ Steam Lua Injector
"""
import os
import psutil
from config import Config
from lua_handler import LuaHandler

class SteamInjector:
    def __init__(self):
        self.config = Config()
        self.current_game = None
        self.lua_handler = None
        
    def is_steam_running(self):
        """التحقق من تشغيل Steam"""
        for proc in psutil.process_iter(['pid', 'name']):
            if proc.info['name'] == 'steam.exe':
                return True
        return False
    
    def get_game_info(self, app_id):
        """الحصول على معلومات العبة"""
        game_path = self.config.get_game_path(app_id)
        if not game_path:
            return None
            
        game_name = os.path.basename(game_path)
        return {
            'app_id': app_id,
            'name': game_name,
            'path': game_path,
            'exists': os.path.exists(game_path)
        }
    
    def load_game(self, app_id):
        """تحميل العبة للعمل عليها"""
        game_info = self.get_game_info(app_id)
        if not game_info or not game_info['exists']:
            return False
            
        self.current_game = game_info
        self.lua_handler = LuaHandler(game_info['path'])
        return True
    
    def get_lua_files(self):
        """الحصول على قائمة ملفات Lua في العبة الحالية"""
        if not self.lua_handler:
            return []
        return self.lua_handler.find_lua_files()
    
    def inject_code(self, target_file, code, position="end"):
        """حقن كود في ملف Lua"""
        if not self.lua_handler:
            return False
        return self.lua_handler.inject_lua_code(target_file, code, position)
    
    def replace_file(self, target_file, new_file):
        """استبدال ملف Lua"""
        if not self.lua_handler:
            return False
        return self.lua_handler.replace_lua_file(target_file, new_file)
    
    def create_backup(self, file_path):
        """إنشاء نسخة احتياطية"""
        if not self.lua_handler:
            return False
        return self.lua_handler.create_backup(file_path)
    
    def restore_backup(self, file_path):
        """استعادة نسخة احتياطية"""
        if not self.lua_handler:
            return False
        return self.lua_handler.restore_backup(file_path)
    
    def get_file_content(self, file_path):
        """قراءة محتوى ملف"""
        if not self.lua_handler:
            return None
        return self.lua_handler.get_lua_content(file_path)
    
    def is_game_running(self, app_id):
        """التحقق من تشغيل العبة"""
        # يمكن تحسين هذه الوظيفة للتحقق من العمليات الجارية
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if proc.info['exe'] and self.current_game:
                    if self.current_game['path'] in proc.info['exe']:
                        return True
            except:
                continue
        return False
    
    def get_steam_library_folders(self):
        """الحصول على مجلدات مكتبة Steam"""
        library_folders = []
        if not self.config.steamapps_path:
            return library_folders
            
        # إضافة المجلد الافتراضي
        library_folders.append(self.config.steamapps_path)
        
        # البحث عن مجلدات إضافية في ملف libraryfolders.vdf
        vdf_path = os.path.join(self.config.steamapps_path, "libraryfolders.vdf")
        if os.path.exists(vdf_path):
            try:
                with open(vdf_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # استخراج مسارات المجلدات (تحليل بسيط لملف VDF)
                lines = content.split('\n')
                for line in lines:
                    if '"path"' in line:
                        path = line.split('"')[3]
                        steamapps_path = os.path.join(path, "steamapps")
                        if os.path.exists(steamapps_path) and steamapps_path not in library_folders:
                            library_folders.append(steamapps_path)
            except:
                pass
                
        return library_folders
