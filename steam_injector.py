"""
كلاس رئيسي لـ Steam Lua Injector
"""
import os
import psutil
import requests
import json
import time
from config import Config
from lua_handler import LuaHandler

class SteamInjector:
    def __init__(self):
        self.config = Config()
        self.current_game = None
        self.lua_handler = None
        self.all_games_cache = None
        self.installed_games_cache = None
        
    def is_steam_running(self):
        """التحقق من تشغيل Steam"""
        for proc in psutil.process_iter(['pid', 'name']):
            if proc.info['name'] == 'steam.exe':
                return True
        return False
    
    def get_game_info(self, app_id):
        """الحصول على معلومات العبة"""
        game_path = self.config.get_game_path(app_id)
        if not game_path:
            return None
            
        game_name = os.path.basename(game_path)
        return {
            'app_id': app_id,
            'name': game_name,
            'path': game_path,
            'exists': os.path.exists(game_path)
        }
    
    def load_game(self, app_id):
        """تحميل العبة للعمل عليها"""
        game_info = self.get_game_info(app_id)
        if not game_info or not game_info['exists']:
            return False
            
        self.current_game = game_info
        self.lua_handler = LuaHandler(game_info['path'])
        return True
    
    def get_lua_files(self):
        """الحصول على قائمة ملفات Lua في العبة الحالية"""
        if not self.lua_handler:
            return []
        return self.lua_handler.find_lua_files()
    
    def inject_code(self, target_file, code, position="end"):
        """حقن كود في ملف Lua"""
        if not self.lua_handler:
            return False
        return self.lua_handler.inject_lua_code(target_file, code, position)
    
    def replace_file(self, target_file, new_file):
        """استبدال ملف Lua"""
        if not self.lua_handler:
            return False
        return self.lua_handler.replace_lua_file(target_file, new_file)
    
    def create_backup(self, file_path):
        """إنشاء نسخة احتياطية"""
        if not self.lua_handler:
            return False
        return self.lua_handler.create_backup(file_path)
    
    def restore_backup(self, file_path):
        """استعادة نسخة احتياطية"""
        if not self.lua_handler:
            return False
        return self.lua_handler.restore_backup(file_path)
    
    def get_file_content(self, file_path):
        """قراءة محتوى ملف"""
        if not self.lua_handler:
            return None
        return self.lua_handler.get_lua_content(file_path)
    
    def is_game_running(self, app_id):
        """التحقق من تشغيل العبة"""
        # يمكن تحسين هذه الوظيفة للتحقق من العمليات الجارية
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if proc.info['exe'] and self.current_game:
                    if self.current_game['path'] in proc.info['exe']:
                        return True
            except:
                continue
        return False
    
    def get_steam_library_folders(self):
        """الحصول على مجلدات مكتبة Steam"""
        library_folders = []
        if not self.config.steamapps_path:
            return library_folders
            
        # إضافة المجلد الافتراضي
        library_folders.append(self.config.steamapps_path)
        
        # البحث عن مجلدات إضافية في ملف libraryfolders.vdf
        vdf_path = os.path.join(self.config.steamapps_path, "libraryfolders.vdf")
        if os.path.exists(vdf_path):
            try:
                with open(vdf_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # استخراج مسارات المجلدات (تحليل بسيط لملف VDF)
                lines = content.split('\n')
                for line in lines:
                    if '"path"' in line:
                        path = line.split('"')[3]
                        steamapps_path = os.path.join(path, "steamapps")
                        if os.path.exists(steamapps_path) and steamapps_path not in library_folders:
                            library_folders.append(steamapps_path)
            except:
                pass

        return library_folders

    def get_all_steam_games(self, force_refresh=False):
        """جلب قائمة جميع الألعاب من Steam API"""
        if self.all_games_cache and not force_refresh:
            return self.all_games_cache

        try:
            print("جاري جلب قائمة الألعاب من Steam...")
            url = "https://api.steampowered.com/ISteamApps/GetAppList/v0002/?format=json"
            response = requests.get(url, timeout=30)

            if response.status_code == 200:
                data = response.json()
                games = data['applist']['apps']

                # تصفية الألعاب (إزالة DLC والمحتوى الإضافي)
                filtered_games = []
                for game in games:
                    name = game['name'].lower()
                    # تجاهل DLC والمحتوى الإضافي
                    if not any(keyword in name for keyword in ['dlc', 'soundtrack', 'demo', 'beta', 'test']):
                        filtered_games.append(game)

                self.all_games_cache = filtered_games
                print(f"تم جلب {len(filtered_games)} لعبة")
                return filtered_games
            else:
                print(f"خطأ في جلب البيانات: {response.status_code}")
                return []

        except Exception as e:
            print(f"خطأ في الاتصال بـ Steam API: {e}")
            return []

    def search_games(self, query, limit=50):
        """البحث في الألعاب"""
        if not self.all_games_cache:
            self.get_all_steam_games()

        if not self.all_games_cache:
            return []

        query = query.lower().strip()
        if not query:
            return self.all_games_cache[:limit]

        results = []
        for game in self.all_games_cache:
            if query in game['name'].lower():
                results.append(game)
                if len(results) >= limit:
                    break

        return results

    def get_installed_games(self, force_refresh=False):
        """جلب قائمة الألعاب المثبتة"""
        if self.installed_games_cache and not force_refresh:
            return self.installed_games_cache

        installed_games = []
        library_folders = self.get_steam_library_folders()

        for steamapps_path in library_folders:
            if not os.path.exists(steamapps_path):
                continue

            # البحث في ملفات appmanifest
            for file in os.listdir(steamapps_path):
                if file.startswith('appmanifest_') and file.endswith('.acf'):
                    try:
                        app_id = file.replace('appmanifest_', '').replace('.acf', '')
                        manifest_path = os.path.join(steamapps_path, file)

                        with open(manifest_path, 'r', encoding='utf-8') as f:
                            content = f.read()

                        # استخراج اسم العبة ومجلد التثبيت
                        game_name = None
                        install_dir = None

                        for line in content.split('\n'):
                            if '"name"' in line:
                                game_name = line.split('"')[3]
                            elif '"installdir"' in line:
                                install_dir = line.split('"')[3]

                        if game_name and install_dir:
                            game_path = os.path.join(steamapps_path, "common", install_dir)
                            if os.path.exists(game_path):
                                installed_games.append({
                                    'appid': app_id,
                                    'name': game_name,
                                    'path': game_path,
                                    'install_dir': install_dir
                                })

                    except Exception as e:
                        print(f"خطأ في قراءة ملف {file}: {e}")
                        continue

        self.installed_games_cache = installed_games
        print(f"تم العثور على {len(installed_games)} لعبة مثبتة")
        return installed_games

    def inject_all_games(self, lua_code, position="end", file_pattern="*.lua"):
        """حقن كود في جميع الألعاب المثبتة"""
        installed_games = self.get_installed_games()
        results = []

        for game in installed_games:
            try:
                print(f"معالجة العبة: {game['name']}")

                # تحميل العبة
                if self.load_game(game['appid']):
                    lua_files = self.get_lua_files()

                    game_results = {
                        'game': game['name'],
                        'appid': game['appid'],
                        'files_processed': 0,
                        'files_failed': 0,
                        'errors': []
                    }

                    for lua_file in lua_files:
                        try:
                            if self.inject_code(lua_file['full_path'], lua_code, position):
                                game_results['files_processed'] += 1
                            else:
                                game_results['files_failed'] += 1
                                game_results['errors'].append(f"فشل في حقن {lua_file['name']}")
                        except Exception as e:
                            game_results['files_failed'] += 1
                            game_results['errors'].append(f"خطأ في {lua_file['name']}: {str(e)}")

                    results.append(game_results)
                    print(f"تم معالجة {game_results['files_processed']} ملف في {game['name']}")

                else:
                    results.append({
                        'game': game['name'],
                        'appid': game['appid'],
                        'files_processed': 0,
                        'files_failed': 0,
                        'errors': ['فشل في تحميل العبة']
                    })

            except Exception as e:
                print(f"خطأ في معالجة العبة {game['name']}: {e}")
                results.append({
                    'game': game['name'],
                    'appid': game['appid'],
                    'files_processed': 0,
                    'files_failed': 0,
                    'errors': [str(e)]
                })

        return results
